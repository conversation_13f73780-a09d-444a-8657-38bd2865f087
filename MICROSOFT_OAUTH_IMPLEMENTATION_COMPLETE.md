# Microsoft OAuth2 Implementation - Complete Guide

## Overview

This document provides a comprehensive guide for the Microsoft OAuth2 authentication implementation in the Flowise application. The implementation supports both personal Microsoft accounts and business/organizational accounts (Microsoft 365/Azure AD) as an additional login option alongside the existing username/password authentication.

## ✅ Features Implemented

### Backend Implementation
- ✅ Microsoft OAuth service with token validation
- ✅ User creation/authentication logic with Microsoft Graph API integration
- ✅ Proper error handling and logging
- ✅ JWT token generation for authenticated users
- ✅ Uses existing User entity fields (no database schema changes)
- ✅ RESTful API endpoint for Microsoft OAuth login
- ✅ Whitelist configuration for OAuth endpoints

### Frontend Implementation
- ✅ MSAL (Microsoft Authentication Library) integration
- ✅ Microsoft login button component
- ✅ Dedicated redirect page for OAuth callback handling
- ✅ Comprehensive error handling and user feedback
- ✅ Debug logging utility for troubleshooting
- ✅ Integration with existing login page

### Security & Configuration
- ✅ Environment variable configuration for sensitive data
- ✅ Proper token validation with Microsoft Graph API
- ✅ User account linking and creation logic
- ✅ Secure JWT token generation

## Architecture

### Backend Components

1. **Microsoft OAuth Service** (`packages/server/src/services/microsoftOAuth/index.ts`)
   - Validates access tokens with Microsoft Graph API
   - Manages user creation and account linking using existing User entity fields
   - Generates JWT tokens for authenticated users
   - Uses `displayPrefixes` field to store Microsoft display name

2. **Microsoft OAuth Controller** (`packages/server/src/controllers/microsoftOAuth/index.ts`)
   - Exposes REST API endpoint for Microsoft OAuth login
   - Handles request validation and response formatting
   - Integrates with the Microsoft OAuth service

3. **Microsoft OAuth Routes** (`packages/server/src/routes/microsoftOAuth/index.ts`)
   - Defines API routes for Microsoft OAuth
   - Integrated into main application routes

### Frontend Components

1. **MSAL Hook** (`packages/ui/src/hooks/useMSAL.js`)
   - Manages MSAL instance initialization
   - Handles login redirects and callback processing
   - Provides authentication state management

2. **Microsoft Login Button** (`packages/ui/src/components/MicrosoftLoginButton.jsx`)
   - Renders Microsoft login button with proper styling
   - Integrates with MSAL hook for authentication flow
   - Handles loading states and error feedback

3. **Microsoft Redirect Handler** (`packages/ui/src/views/MicrosoftRedirect/index.jsx`)
   - Dedicated page for handling OAuth callback at `/redirect`
   - Processes authentication response from Microsoft
   - Manages user session and navigation

## Configuration

### Azure Portal Setup

1. **App Registration:**
   - Navigate to Azure Portal > Azure Active Directory > App registrations
   - Create new registration with name "Flowise OAuth2"
   - Set supported account types to "Accounts in any organizational directory and personal Microsoft accounts"
   - Configure redirect URI: `http://localhost:3000/redirect` (development) or `https://yourdomain.com/redirect` (production)

2. **API Permissions:**
   - Add Microsoft Graph > Delegated permissions > User.Read
   - Grant admin consent for the permissions

3. **Client Secret:**
   - Generate client secret in "Certificates & secrets" section
   - Copy the secret value (only shown once)

### Environment Variables

#### Backend (.env)
```env
# Microsoft OAuth Configuration
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret
MICROSOFT_TENANT_ID=common
```

#### Frontend (.env)
```env
# Microsoft OAuth Configuration
REACT_APP_MICROSOFT_CLIENT_ID=your-microsoft-client-id
REACT_APP_MICROSOFT_TENANT_ID=common
```

## Authentication Flow

1. **User clicks "Login with Microsoft" button** on the login page
2. **Frontend redirects to Microsoft OAuth endpoint** using MSAL
3. **User authenticates with Microsoft** (personal or business account)
4. **Microsoft redirects to `/redirect` page** with authorization code
5. **MSAL exchanges code for access token** automatically
6. **Frontend sends access token to backend API** (`/api/v1/microsoft-oauth/login`)
7. **Backend validates token with Microsoft Graph API** to get user info
8. **Backend creates/updates user and generates JWT tokens** using existing User entity
9. **Frontend stores tokens and redirects to application**

## Database Schema

### User Entity Fields Used
- `username`: Set to user's email address
- `email`: Microsoft account email (mail or userPrincipalName)
- `password`: Random hash for OAuth users (not used for login)
- `displayPrefixes`: Stores Microsoft display name
- `groupname`: Set to "Microsoft_Users" for OAuth users
- `role`: Default to USER
- `active`: Set to true

**Note:** No new database fields were added. The implementation uses existing User entity fields creatively.

## API Endpoints

### Microsoft OAuth Login
```
POST /api/v1/microsoft-oauth/login
Content-Type: application/json

{
  "accessToken": "microsoft_access_token_here"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user-uuid",
    "username": "<EMAIL>",
    "email": "<EMAIL>",
    "displayName": "User Display Name",
    "role": "USER",
    "groupname": "Microsoft_Users",
    "active": true
  },
  "accessToken": "jwt_access_token",
  "refreshToken": "jwt_refresh_token"
}
```

## Security Considerations

1. **Token Validation:** All Microsoft access tokens are validated against Microsoft Graph API
2. **User Linking:** Existing users can be linked to Microsoft accounts via email matching
3. **Account Creation:** New users are created with secure random passwords
4. **JWT Security:** Standard JWT tokens are used for session management
5. **Environment Variables:** Sensitive configuration is stored in environment variables
6. **No Database Changes:** Uses existing User entity fields to avoid schema modifications

## Dependencies Installed

### Backend
- `@azure/msal-node`: Microsoft Authentication Library for Node.js

### Frontend
- `@azure/msal-browser`: Microsoft Authentication Library for browsers
- `@azure/msal-react`: React wrapper for MSAL

## Files Created/Modified

### Backend Files Created
- `packages/server/src/services/microsoftOAuth/index.ts`
- `packages/server/src/controllers/microsoftOAuth/index.ts`
- `packages/server/src/routes/microsoftOAuth/index.ts`

### Frontend Files Created
- `packages/ui/src/hooks/useMSAL.js`
- `packages/ui/src/components/MicrosoftLoginButton.jsx`
- `packages/ui/src/views/MicrosoftRedirect/index.jsx`

### Files Modified
- `packages/server/src/routes/index.ts` - Added Microsoft OAuth routes
- `packages/server/src/index.ts` - Added whitelist for OAuth endpoints
- `packages/server/.env` - Added Microsoft OAuth environment variables
- `packages/ui/src/routes/LoginRoute.jsx` - Added redirect route
- `packages/ui/src/views/Login/LoginDefault.jsx` - Added Microsoft login button
- `packages/ui/src/api/user.js` - Added Microsoft OAuth API method
- `packages/ui/.env` - Added Microsoft OAuth environment variables

## Testing

### Manual Testing Checklist
- [ ] Microsoft login button appears on login page
- [ ] Clicking button redirects to Microsoft OAuth
- [ ] Successful authentication redirects to `/redirect` page
- [ ] User is created/updated in database using existing fields
- [ ] JWT tokens are generated and stored
- [ ] User is redirected to application
- [ ] Logout functionality works correctly
- [ ] Existing username/password login still works

### Error Scenarios
- [ ] Invalid access token handling
- [ ] Network error handling
- [ ] User cancellation handling
- [ ] Expired token handling

## Troubleshooting

### Common Issues

1. **MSAL Initialization Error:**
   - Ensure REACT_APP_MICROSOFT_CLIENT_ID is set correctly
   - Check Azure Portal redirect URI configuration

2. **Token Validation Failed:**
   - Verify MICROSOFT_CLIENT_SECRET is correct
   - Check Microsoft Graph API permissions

3. **Redirect URI Mismatch:**
   - Ensure Azure Portal redirect URI matches your domain
   - Check both development and production URLs

### Debug Mode

The implementation includes comprehensive debug logging:
- All Microsoft OAuth operations are logged with 🔐 prefix
- Frontend debug utility available via `microsoftOAuthDebugger`
- Enable debug mode: `localStorage.setItem('microsoft-oauth-debug', 'true')`

## Next Steps

1. **Configure Azure Portal** with your actual client ID and secret
2. **Update environment variables** with real values
3. **Test the implementation** with both personal and business Microsoft accounts
4. **Deploy to production** with proper HTTPS redirect URIs
5. **Monitor logs** for any issues during initial rollout

## Support

For issues and troubleshooting:
1. Check the debug logs using the microsoftOAuthDebugger utility
2. Verify Azure Portal configuration
3. Ensure all environment variables are set correctly
4. Review server logs for backend authentication issues
