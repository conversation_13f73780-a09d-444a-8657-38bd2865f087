module.exports = {
  extends: [
    'eslint:recommended',
    'plugin:markdown/recommended',
    'plugin:react/recommended',
    'plugin:react/jsx-runtime',
    'plugin:react-hooks/recommended',
    'plugin:jsx-a11y/recommended',
    'plugin:prettier/recommended'
  ],
  settings: {
    react: {
      version: 'detect'
    }
  },
  parser: '@typescript-eslint/parser',
  ignorePatterns: ['**/node_modules', '**/dist', '**/build', '**/package-lock.json'],
  plugins: ['unused-imports'],
  rules: {
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    'no-unused-vars': 'off',
    'unused-imports/no-unused-imports': 'warn',
    'unused-imports/no-unused-vars': ['warn', { vars: 'all', varsIgnorePattern: '^_', args: 'after-used', argsIgnorePattern: '^_' }],
    'no-undef': 'off',
    'no-console': [process.env.CI ? 'error' : 'warn', { allow: ['warn', 'error', 'info'] }],
    'prettier/prettier': [
      'error',
      {
        printWidth: 140,
        singleQuote: true,
        jsxSingleQuote: true,
        trailingComma: 'none',
        tabWidth: 2,
        semi: false,
        endOfLine: 'auto'
      }
    ]
  }
}
