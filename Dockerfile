# Sử dụng image Node.js từ registry nội bộ
FROM **************:5000/node:20-alpine

# C<PERSON><PERSON> các gói cần thiết
RUN apk add --update libc6-compat python3 make g++ \
    && apk add --no-cache build-base cairo-dev pango-dev \
    && apk add --no-cache chromium

# Cài đặt pnpm toàn cục
RUN npm install -g pnpm

# Cấu hình môi trường Puppeteer
ENV PUPPETEER_SKIP_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Tăng giới hạn bộ nhớ Node.js
ENV NODE_OPTIONS=--max-old-space-size=8192

# Thiết lập thư mục làm việc
WORKDIR /usr/src

# Copy mã nguồn vào image
COPY . .

# Cài đặt phụ thuộc và build app
RUN pnpm install
RUN pnpm build

# Mở cổng 3000
EXPOSE 3000

# Lệnh khởi động ứng dụng
CMD [ "pnpm", "start" ]
