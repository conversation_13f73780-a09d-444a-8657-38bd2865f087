{"name": "flowise", "version": "2.2.1", "private": true, "homepage": "https://flowiseai.com", "workspaces": ["packages/*", "flowise", "ui", "components", "api-documentation"], "scripts": {"build": "turbo run build", "build-force": "pnpm clean && turbo run build --force", "dev": "turbo run dev --parallel", "start": "run-script-os", "start:windows": "cd packages/server/bin && run start", "start:default": "cd packages/server/bin && ./run start", "clean": "pnpm --filter \"./packages/**\" clean", "nuke": "pnpm --filter \"./packages/**\" nuke && rimraf node_modules .turbo", "format": "prettier --write \"**/*.{ts,tsx,md,json,js,jsx,css,scss}\"", "lint": "eslint \"**/*.{js,jsx,ts,tsx,json,md}\"", "lint-fix": "pnpm lint --fix", "quick": "pretty-quick --staged", "postinstall": "husky install", "migration:create": "pnpm typeorm migration:create", "dev:server": "pnpm --filter ./packages/server dev", "dev:ui": "pnpm --filter ./packages/ui dev", "dev:components": "pnpm --filter ./packages/components dev", "bench:model": "bun run ./benchModel.ts"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md}": "eslint --fix"}, "devDependencies": {"@babel/preset-env": "^7.19.4", "@babel/preset-typescript": "7.18.6", "@types/express": "^4.17.13", "@typescript-eslint/typescript-estree": "^7.13.1", "eslint": "^8.24.0", "eslint-config-prettier": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-markdown": "^3.0.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.26.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^8.0.1", "kill-port": "^2.0.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "pretty-quick": "^3.1.3", "rimraf": "^3.0.2", "run-script-os": "^1.1.6", "turbo": "1.10.16", "typescript": "^5.4.5"}, "pnpm": {"onlyBuiltDependencies": ["faiss-node", "sqlite3"], "overrides": {"set-value": "^3.0.3"}}, "engines": {"node": ">=18.15.0 <19.0.0 || ^20", "pnpm": ">=9"}, "resolutions": {"@google/generative-ai": "^0.15.0", "@grpc/grpc-js": "^1.10.10", "@langchain/core": "0.3.18", "@qdrant/openapi-typescript-fetch": "1.2.6", "openai": "4.57.3", "protobufjs": "7.4.0"}, "eslintIgnore": ["**/dist", "**/node_modules", "**/build", "**/package-lock.json"], "prettier": {"printWidth": 140, "singleQuote": true, "jsxSingleQuote": true, "trailingComma": "none", "tabWidth": 2, "semi": false, "endOfLine": "auto"}, "babel": {"presets": ["@babel/preset-typescript", ["@babel/preset-env", {"targets": {"node": "current"}}]]}, "packageManager": "pnpm@10.10.0"}