const { execSync } = require('child_process')
const path = require('path')

console.log('Running Microsoft OAuth2 migration...')

try {
  // Run the migration
  execSync('npm run typeorm:migration-run', { 
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit'
  })
  
  console.log('Migration completed successfully!')
} catch (error) {
  console.error('Migration failed:', error.message)
  process.exit(1)
} 