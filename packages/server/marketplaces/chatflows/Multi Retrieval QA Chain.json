{"description": "A chain that automatically picks an appropriate vector store retriever from multiple different vector databases", "usecases": ["Documents QnA"], "framework": ["Langchain"], "nodes": [{"width": 300, "height": 506, "id": "vectorStoreRetriever_0", "position": {"x": 712.9322670298264, "y": 860.5462810572917}, "type": "customNode", "data": {"id": "vectorStoreRetriever_0", "label": "Vector Store Retriever", "version": 1, "name": "vectorStoreRetriever", "type": "VectorStoreRetriever", "baseClasses": ["VectorStoreRetriever"], "category": "Retrievers", "description": "Store vector store as retriever. Used with MultiRetrievalQAChain", "inputParams": [{"label": "Retriever Name", "name": "name", "type": "string", "placeholder": "netflix movies", "id": "vectorStoreRetriever_0-input-name-string"}, {"label": "Retriever Description", "name": "description", "type": "string", "rows": 3, "description": "Description of when to use the vector store retriever", "placeholder": "Good for answering questions about netflix movies", "id": "vectorStoreRetriever_0-input-description-string"}], "inputAnchors": [{"label": "Vector Store", "name": "vectorStore", "type": "VectorStore", "id": "vectorStoreRetriever_0-input-vectorStore-VectorStore"}], "inputs": {"vectorStore": "{{supabase_0.data.instance}}", "name": "aqua teen", "description": "Good for answering questions about Aqua Teen Hunger Force theme song"}, "outputAnchors": [{"id": "vectorStoreRetriever_0-output-vectorStoreRetriever-VectorStoreRetriever", "name": "vectorStoreRetriever", "label": "VectorStoreRetriever", "type": "VectorStoreRetriever"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 712.9322670298264, "y": 860.5462810572917}, "dragging": false}, {"width": 300, "height": 429, "id": "multiRetrievalQAChain_0", "position": {"x": 1563.0150452201099, "y": 460.78375893303934}, "type": "customNode", "data": {"id": "multiRetrievalQAChain_0", "label": "Multi Retrieval QA Chain", "version": 2, "name": "multiRetrievalQAChain", "type": "MultiRetrievalQAChain", "baseClasses": ["MultiRetrievalQAChain", "MultiRouteChain", "BaseChain", "BaseLangChain"], "category": "Chains", "description": "QA Chain that automatically picks an appropriate vector store from multiple retrievers", "inputParams": [{"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "multiRetrievalQAChain_0-input-model-BaseLanguageModel"}, {"label": "Vector Store Retriever", "name": "vectorStoreRetriever", "type": "VectorStoreRetriever", "list": true, "id": "multiRetrievalQAChain_0-input-vectorStoreRetriever-VectorStoreRetriever"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "multiRetrievalQAChain_0-input-inputModeration-Moderation"}], "inputs": {"inputModeration": "", "model": "{{chatOpenAI_0.data.instance}}", "vectorStoreRetriever": ["{{vectorStoreRetriever_0.data.instance}}", "{{vectorStoreRetriever_1.data.instance}}", "{{vectorStoreRetriever_2.data.instance}}"]}, "outputAnchors": [{"id": "multiRetrievalQAChain_0-output-multiRetrievalQAChain-MultiRetrievalQAChain|MultiRouteChain|BaseChain|BaseLangChain", "name": "multiRetrievalQAChain", "label": "MultiRetrievalQAChain", "type": "MultiRetrievalQAChain | MultiRouteChain | BaseChain | BaseLangChain"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1563.0150452201099, "y": 460.78375893303934}, "dragging": false}, {"width": 300, "height": 506, "id": "vectorStoreRetriever_1", "position": {"x": 711.4902931206071, "y": 315.2414600651632}, "type": "customNode", "data": {"id": "vectorStoreRetriever_1", "label": "Vector Store Retriever", "version": 1, "name": "vectorStoreRetriever", "type": "VectorStoreRetriever", "baseClasses": ["VectorStoreRetriever"], "category": "Retrievers", "description": "Store vector store as retriever. Used with MultiRetrievalQAChain", "inputParams": [{"label": "Retriever Name", "name": "name", "type": "string", "placeholder": "netflix movies", "id": "vectorStoreRetriever_1-input-name-string"}, {"label": "Retriever Description", "name": "description", "type": "string", "rows": 3, "description": "Description of when to use the vector store retriever", "placeholder": "Good for answering questions about netflix movies", "id": "vectorStoreRetriever_1-input-description-string"}], "inputAnchors": [{"label": "Vector Store", "name": "vectorStore", "type": "VectorStore", "id": "vectorStoreRetriever_1-input-vectorStore-VectorStore"}], "inputs": {"vectorStore": "{{chroma_0.data.instance}}", "name": "mst3k", "description": "Good for answering questions about Mystery Science Theater 3000 theme song"}, "outputAnchors": [{"id": "vectorStoreRetriever_1-output-vectorStoreRetriever-VectorStoreRetriever", "name": "vectorStoreRetriever", "label": "VectorStoreRetriever", "type": "VectorStoreRetriever"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 711.4902931206071, "y": 315.2414600651632}, "dragging": false}, {"width": 300, "height": 506, "id": "vectorStoreRetriever_2", "position": {"x": 706.0716220151372, "y": -217.51566869136752}, "type": "customNode", "data": {"id": "vectorStoreRetriever_2", "label": "Vector Store Retriever", "version": 1, "name": "vectorStoreRetriever", "type": "VectorStoreRetriever", "baseClasses": ["VectorStoreRetriever"], "category": "Retrievers", "description": "Store vector store as retriever. Used with MultiRetrievalQAChain", "inputParams": [{"label": "Retriever Name", "name": "name", "type": "string", "placeholder": "netflix movies", "id": "vectorStoreRetriever_2-input-name-string"}, {"label": "Retriever Description", "name": "description", "type": "string", "rows": 3, "description": "Description of when to use the vector store retriever", "placeholder": "Good for answering questions about netflix movies", "id": "vectorStoreRetriever_2-input-description-string"}], "inputAnchors": [{"label": "Vector Store", "name": "vectorStore", "type": "VectorStore", "id": "vectorStoreRetriever_2-input-vectorStore-VectorStore"}], "inputs": {"vectorStore": "{{pinecone_0.data.instance}}", "name": "animaniacs", "description": "Good for answering questions about Animaniacs theme song"}, "outputAnchors": [{"id": "vectorStoreRetriever_2-output-vectorStoreRetriever-VectorStoreRetriever", "name": "vectorStoreRetriever", "label": "VectorStoreRetriever", "type": "VectorStoreRetriever"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 706.0716220151372, "y": -217.51566869136752}, "dragging": false}, {"width": 300, "height": 424, "id": "openAIEmbeddings_0", "position": {"x": -212.46977797044045, "y": 252.45726960585722}, "type": "customNode", "data": {"id": "openAIEmbeddings_0", "label": "OpenAI Embeddings", "version": 4, "name": "openAIEmbeddings", "type": "OpenAIEmbeddings", "baseClasses": ["OpenAIEmbeddings", "Embeddings"], "category": "Embeddings", "description": "OpenAI API to generate embeddings for a given text", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "openAIEmbeddings_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "text-embedding-ada-002", "id": "openAIEmbeddings_0-input-modelName-asyncOptions"}, {"label": "Strip New Lines", "name": "stripNewLines", "type": "boolean", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-stripNewLines-boolean"}, {"label": "<PERSON><PERSON> Si<PERSON>", "name": "batchSize", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-batchSize-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-basepath-string"}, {"label": "Dimensions", "name": "dimensions", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-dimensions-number"}], "inputAnchors": [], "inputs": {"modelName": "text-embedding-ada-002", "stripNewLines": "", "batchSize": "", "timeout": "", "basepath": "", "dimensions": ""}, "outputAnchors": [{"id": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "name": "openAIEmbeddings", "label": "OpenAIEmbeddings", "description": "OpenAI API to generate embeddings for a given text", "type": "OpenAIEmbeddings | Embeddings"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": -212.46977797044045, "y": 252.45726960585722}, "dragging": false}, {"width": 300, "height": 670, "id": "chatOpenAI_0", "position": {"x": 1166.929741805626, "y": -297.9691758089252}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo", "temperature": 0.9, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1166.929741805626, "y": -297.9691758089252}, "dragging": false}, {"width": 300, "height": 606, "id": "pinecone_0", "position": {"x": 268.04147939086755, "y": -407.5681206851249}, "type": "customNode", "data": {"id": "pinecone_0", "label": "Pinecone", "version": 3, "name": "pinecone", "type": "Pinecone", "baseClasses": ["Pinecone", "VectorStoreRetriever", "BaseRetriever"], "category": "Vector Stores", "description": "Upsert embedded data and perform similarity or mmr search using Pinecone, a leading fully managed hosted vector database", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["pineconeApi"], "id": "pinecone_0-input-credential-credential"}, {"label": "Pinecone Index", "name": "pineconeIndex", "type": "string", "id": "pinecone_0-input-pineconeIndex-string"}, {"label": "Pinecone Namespace", "name": "pineconeNamespace", "type": "string", "placeholder": "my-first-namespace", "additionalParams": true, "optional": true, "id": "pinecone_0-input-pineconeNamespace-string"}, {"label": "Pinecone Metadata Filter", "name": "pineconeMetadataFilter", "type": "json", "optional": true, "additionalParams": true, "id": "pinecone_0-input-pineconeMetadataFilter-json"}, {"label": "Top K", "name": "topK", "description": "Number of top results to fetch. De<PERSON><PERSON> to 4", "placeholder": "4", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_0-input-topK-number"}, {"label": "Search Type", "name": "searchType", "type": "options", "default": "similarity", "options": [{"label": "Similarity", "name": "similarity"}, {"label": "<PERSON>ginal Relevance", "name": "mmr"}], "additionalParams": true, "optional": true, "id": "pinecone_0-input-searchType-options"}, {"label": "Fetch K (for MMR Search)", "name": "fetchK", "description": "Number of initial documents to fetch for MMR reranking. Default to 20. Used only when the search type is MMR", "placeholder": "20", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_0-input-fetchK-number"}, {"label": "Lambda (for MMR Search)", "name": "lambda", "description": "Number between 0 and 1 that determines the degree of diversity among the results, where 0 corresponds to maximum diversity and 1 to minimum diversity. Used only when the search type is MMR", "placeholder": "0.5", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_0-input-lambda-number"}], "inputAnchors": [{"label": "Document", "name": "document", "type": "Document", "list": true, "optional": true, "id": "pinecone_0-input-document-Document"}, {"label": "Embeddings", "name": "embeddings", "type": "Embeddings", "id": "pinecone_0-input-embeddings-Embeddings"}, {"label": "Record Manager", "name": "recordManager", "type": "RecordManager", "description": "Keep track of the record to prevent duplication", "optional": true, "id": "pinecone_0-input-recordManager-RecordManager"}], "inputs": {"document": "", "embeddings": "{{openAIEmbeddings_0.data.instance}}", "recordManager": "", "pineconeIndex": "", "pineconeNamespace": "", "pineconeMetadataFilter": "", "topK": "", "searchType": "similarity", "fetchK": "", "lambda": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "pinecone_0-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "name": "retriever", "label": "Pinecone Retriever", "description": "", "type": "Pinecone | VectorStoreRetriever | BaseRetriever"}, {"id": "pinecone_0-output-vectorStore-Pinecone|VectorStore", "name": "vectorStore", "label": "Pinecone Vector Store", "description": "", "type": "Pinecone | VectorStore"}], "default": "retriever"}], "outputs": {"output": "vectorStore"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 268.04147939086755, "y": -407.5681206851249}, "dragging": false}, {"width": 300, "height": 704, "id": "chroma_0", "position": {"x": 271.26687710753146, "y": 240.7980496352519}, "type": "customNode", "data": {"id": "chroma_0", "label": "Chroma", "version": 2, "name": "chroma", "type": "Chroma", "baseClasses": ["Chroma", "VectorStoreRetriever", "BaseRetriever"], "category": "Vector Stores", "description": "Upsert embedded data and perform similarity search upon query using Chroma, an open-source embedding database", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "description": "Only needed if you have chroma on cloud services with X-Api-key", "optional": true, "credentialNames": ["chromaApi"], "id": "chroma_0-input-credential-credential"}, {"label": "Collection Name", "name": "collectionName", "type": "string", "id": "chroma_0-input-collectionName-string"}, {"label": "Chroma URL", "name": "chromaURL", "type": "string", "optional": true, "id": "chroma_0-input-chromaURL-string"}, {"label": "Chroma Metadata Filter", "name": "chromaMetadataFilter", "type": "json", "optional": true, "additionalParams": true, "id": "chroma_0-input-chromaMetadataFilter-json"}, {"label": "Top K", "name": "topK", "description": "Number of top results to fetch. De<PERSON><PERSON> to 4", "placeholder": "4", "type": "number", "additionalParams": true, "optional": true, "id": "chroma_0-input-topK-number"}], "inputAnchors": [{"label": "Document", "name": "document", "type": "Document", "list": true, "optional": true, "id": "chroma_0-input-document-Document"}, {"label": "Embeddings", "name": "embeddings", "type": "Embeddings", "id": "chroma_0-input-embeddings-Embeddings"}, {"label": "Record Manager", "name": "recordManager", "type": "RecordManager", "description": "Keep track of the record to prevent duplication", "optional": true, "id": "chroma_0-input-recordManager-RecordManager"}], "inputs": {"document": "", "embeddings": "{{openAIEmbeddings_0.data.instance}}", "recordManager": "", "collectionName": "", "chromaURL": "", "chromaMetadataFilter": "", "topK": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "chroma_0-output-retriever-Chroma|VectorStoreRetriever|BaseRetriever", "name": "retriever", "label": "Chroma Retriever", "description": "", "type": "Chroma | VectorStoreRetriever | BaseRetriever"}, {"id": "chroma_0-output-vectorStore-Chroma|VectorStore", "name": "vectorStore", "label": "Chroma Vector Store", "description": "", "type": "Chroma | VectorStore"}], "default": "retriever"}], "outputs": {"output": "vectorStore"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 271.26687710753146, "y": 240.7980496352519}, "dragging": false}, {"width": 300, "height": 803, "id": "supabase_0", "position": {"x": 274.75982285806055, "y": 982.5186034037372}, "type": "customNode", "data": {"id": "supabase_0", "label": "Supabase", "version": 4, "name": "supabase", "type": "Supabase", "baseClasses": ["Supabase", "VectorStoreRetriever", "BaseRetriever"], "category": "Vector Stores", "description": "Upsert embedded data and perform similarity or mmr search upon query using Supabase via pgvector extension", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["supabaseApi"], "id": "supabase_0-input-credential-credential"}, {"label": "Supabase Project URL", "name": "supabaseProjUrl", "type": "string", "id": "supabase_0-input-supabaseProjUrl-string"}, {"label": "Table Name", "name": "tableName", "type": "string", "id": "supabase_0-input-tableName-string"}, {"label": "Query Name", "name": "queryName", "type": "string", "id": "supabase_0-input-queryName-string"}, {"label": "Supabase Metadata Filter", "name": "supabaseMetadataFilter", "type": "json", "optional": true, "additionalParams": true, "id": "supabase_0-input-supabaseMetadataFilter-json"}, {"label": "Supabase RPC Filter", "name": "supabaseRPCFilter", "type": "string", "rows": 4, "placeholder": "filter(\"metadata->a::int\", \"gt\", 5)\n.filter(\"metadata->c::int\", \"gt\", 7)\n.filter(\"metadata->>stuff\", \"eq\", \"right\");", "description": "Query builder-style filtering. If this is set, will override the metadata filter. Refer <a href=\"https://js.langchain.com/v0.1/docs/integrations/vectorstores/supabase/#metadata-query-builder-filtering\" target=\"_blank\">here</a> for more information", "optional": true, "additionalParams": true, "id": "supabase_0-input-supabaseRPCFilter-string"}, {"label": "Top K", "name": "topK", "description": "Number of top results to fetch. De<PERSON><PERSON> to 4", "placeholder": "4", "type": "number", "additionalParams": true, "optional": true, "id": "supabase_0-input-topK-number"}, {"label": "Search Type", "name": "searchType", "type": "options", "default": "similarity", "options": [{"label": "Similarity", "name": "similarity"}, {"label": "<PERSON>ginal Relevance", "name": "mmr"}], "additionalParams": true, "optional": true, "id": "supabase_0-input-searchType-options"}, {"label": "Fetch K (for MMR Search)", "name": "fetchK", "description": "Number of initial documents to fetch for MMR reranking. Default to 20. Used only when the search type is MMR", "placeholder": "20", "type": "number", "additionalParams": true, "optional": true, "id": "supabase_0-input-fetchK-number"}, {"label": "Lambda (for MMR Search)", "name": "lambda", "description": "Number between 0 and 1 that determines the degree of diversity among the results, where 0 corresponds to maximum diversity and 1 to minimum diversity. Used only when the search type is MMR", "placeholder": "0.5", "type": "number", "additionalParams": true, "optional": true, "id": "supabase_0-input-lambda-number"}], "inputAnchors": [{"label": "Document", "name": "document", "type": "Document", "list": true, "optional": true, "id": "supabase_0-input-document-Document"}, {"label": "Embeddings", "name": "embeddings", "type": "Embeddings", "id": "supabase_0-input-embeddings-Embeddings"}, {"label": "Record Manager", "name": "recordManager", "type": "RecordManager", "description": "Keep track of the record to prevent duplication", "optional": true, "id": "supabase_0-input-recordManager-RecordManager"}], "inputs": {"document": "", "embeddings": "{{openAIEmbeddings_0.data.instance}}", "recordManager": "", "supabaseProjUrl": "", "tableName": "", "queryName": "", "supabaseMetadataFilter": "", "supabaseRPCFilter": "", "topK": "", "searchType": "similarity", "fetchK": "", "lambda": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "supabase_0-output-retriever-Supabase|VectorStoreRetriever|BaseRetriever", "name": "retriever", "label": "Supabase Retriever", "description": "", "type": "Supabase | VectorStoreRetriever | BaseRetriever"}, {"id": "supabase_0-output-vectorStore-Supabase|VectorStore", "name": "vectorStore", "label": "Supabase Vector Store", "description": "", "type": "Supabase | VectorStore"}], "default": "retriever"}], "outputs": {"output": "vectorStore"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 274.75982285806055, "y": 982.5186034037372}, "dragging": false}, {"id": "stickyNote_0", "position": {"x": 1564.4709721348295, "y": 121.26040803337389}, "type": "stickyNote", "data": {"id": "stickyNote_0", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_0-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Multi Retrieval QA Chain is able to pick which Vector Store Retriever to use based on user question.\n\nHowever it comes with the restriction for not being able to resume follow up conversations because there isn't any memory.\n\nIt is suitable for LLM which doesn't have function calling support.\n\nOtherwise, it is recommended to use Multiple Documents QnA template which uses Tool Agent"}, "outputAnchors": [{"id": "stickyNote_0-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 324, "selected": false, "positionAbsolute": {"x": 1564.4709721348295, "y": 121.26040803337389}, "dragging": false}], "edges": [{"source": "vectorStoreRetriever_0", "sourceHandle": "vectorStoreRetriever_0-output-vectorStoreRetriever-VectorStoreRetriever", "target": "multiRetrievalQAChain_0", "targetHandle": "multiRetrievalQAChain_0-input-vectorStoreRetriever-VectorStoreRetriever", "type": "buttonedge", "id": "vectorStoreRetriever_0-vectorStoreRetriever_0-output-vectorStoreRetriever-VectorStoreRetriever-multiRetrievalQAChain_0-multiRetrievalQAChain_0-input-vectorStoreRetriever-VectorStoreRetriever", "data": {"label": ""}}, {"source": "vectorStoreRetriever_1", "sourceHandle": "vectorStoreRetriever_1-output-vectorStoreRetriever-VectorStoreRetriever", "target": "multiRetrievalQAChain_0", "targetHandle": "multiRetrievalQAChain_0-input-vectorStoreRetriever-VectorStoreRetriever", "type": "buttonedge", "id": "vectorStoreRetriever_1-vectorStoreRetriever_1-output-vectorStoreRetriever-VectorStoreRetriever-multiRetrievalQAChain_0-multiRetrievalQAChain_0-input-vectorStoreRetriever-VectorStoreRetriever", "data": {"label": ""}}, {"source": "vectorStoreRetriever_2", "sourceHandle": "vectorStoreRetriever_2-output-vectorStoreRetriever-VectorStoreRetriever", "target": "multiRetrievalQAChain_0", "targetHandle": "multiRetrievalQAChain_0-input-vectorStoreRetriever-VectorStoreRetriever", "type": "buttonedge", "id": "vectorStoreRetriever_2-vectorStoreRetriever_2-output-vectorStoreRetriever-VectorStoreRetriever-multiRetrievalQAChain_0-multiRetrievalQAChain_0-input-vectorStoreRetriever-VectorStoreRetriever", "data": {"label": ""}}, {"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "multiRetrievalQAChain_0", "targetHandle": "multiRetrievalQAChain_0-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-multiRetrievalQAChain_0-multiRetrievalQAChain_0-input-model-BaseLanguageModel", "data": {"label": ""}}, {"source": "pinecone_0", "sourceHandle": "pinecone_0-output-vectorStore-Pinecone|VectorStore", "target": "vectorStoreRetriever_2", "targetHandle": "vectorStoreRetriever_2-input-vectorStore-VectorStore", "type": "buttonedge", "id": "pinecone_0-pinecone_0-output-vectorStore-Pinecone|VectorStore-vectorStoreRetriever_2-vectorStoreRetriever_2-input-vectorStore-VectorStore", "data": {"label": ""}}, {"source": "chroma_0", "sourceHandle": "chroma_0-output-vectorStore-Chroma|VectorStore", "target": "vectorStoreRetriever_1", "targetHandle": "vectorStoreRetriever_1-input-vectorStore-VectorStore", "type": "buttonedge", "id": "chroma_0-chroma_0-output-vectorStore-Chroma|VectorStore-vectorStoreRetriever_1-vectorStoreRetriever_1-input-vectorStore-VectorStore", "data": {"label": ""}}, {"source": "supabase_0", "sourceHandle": "supabase_0-output-vectorStore-Supabase|VectorStore", "target": "vectorStoreRetriever_0", "targetHandle": "vectorStoreRetriever_0-input-vectorStore-VectorStore", "type": "buttonedge", "id": "supabase_0-supabase_0-output-vectorStore-Supabase|VectorStore-vectorStoreRetriever_0-vectorStoreRetriever_0-input-vectorStore-VectorStore", "data": {"label": ""}}, {"source": "openAIEmbeddings_0", "sourceHandle": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "target": "supabase_0", "targetHandle": "supabase_0-input-embeddings-Embeddings", "type": "buttonedge", "id": "openAIEmbeddings_0-openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings-supabase_0-supabase_0-input-embeddings-Embeddings", "data": {"label": ""}}, {"source": "openAIEmbeddings_0", "sourceHandle": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "target": "chroma_0", "targetHandle": "chroma_0-input-embeddings-Embeddings", "type": "buttonedge", "id": "openAIEmbeddings_0-openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings-chroma_0-chroma_0-input-embeddings-Embeddings", "data": {"label": ""}}, {"source": "openAIEmbeddings_0", "sourceHandle": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "target": "pinecone_0", "targetHandle": "pinecone_0-input-embeddings-Embeddings", "type": "buttonedge", "id": "openAIEmbeddings_0-openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings-pinecone_0-pinecone_0-input-embeddings-Embeddings", "data": {"label": ""}}]}