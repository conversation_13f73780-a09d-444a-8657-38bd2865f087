{"description": "Given API docs, agent automatically decide which API to call, generating url and body request from conversation", "framework": ["Langchain"], "usecases": ["Interacting with API"], "nodes": [{"width": 300, "height": 460, "id": "getApiChain_0", "position": {"x": 1222.6923202234623, "y": 359.97676456347756}, "type": "customNode", "data": {"id": "getApiChain_0", "label": "GET API Chain", "version": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GETApiChain", "baseClasses": ["GETApiChain", "BaseChain", "BaseLangChain"], "category": "Chains", "description": "Chain to run queries against GET API", "inputParams": [{"label": "API Documentation", "name": "apiDocs", "type": "string", "description": "Description of how API works. Please refer to more <a target=\"_blank\" href=\"https://github.com/langchain-ai/langchain/blob/master/libs/langchain/langchain/chains/api/open_meteo_docs.py\">examples</a>", "rows": 4, "id": "getApiChain_0-input-apiDocs-string"}, {"label": "Headers", "name": "headers", "type": "json", "additionalParams": true, "optional": true, "id": "getApiChain_0-input-headers-json"}, {"label": "URL Prompt", "name": "urlPrompt", "type": "string", "description": "Prompt used to tell LLMs how to construct the URL. Must contains {api_docs} and {question}", "default": "You are given the below API Documentation:\n{api_docs}\nUsing this documentation, generate the full API url to call for answering the user question.\nYou should build the API url in order to get a response that is as short as possible, while still getting the necessary information to answer the question. Pay attention to deliberately exclude any unnecessary pieces of data in the API call.\n\nQuestion:{question}\nAPI url:", "rows": 4, "additionalParams": true, "id": "getApiChain_0-input-urlPrompt-string"}, {"label": "Answer Prompt", "name": "ansPrompt", "type": "string", "description": "Prompt used to tell LLMs how to return the API response. Must contains {api_response}, {api_url}, and {question}", "default": "Given this {api_response} response for {api_url}. use the given response to answer this {question}", "rows": 4, "additionalParams": true, "id": "getApiChain_0-input-ansPrompt-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "getApiChain_0-input-model-BaseLanguageModel"}], "inputs": {"model": "{{chatOpenAI_1.data.instance}}", "apiDocs": "BASE URL: https://api.open-meteo.com/\n\nAPI Documentation\nThe API endpoint /v1/forecast accepts a geographical coordinate, a list of weather variables and responds with a JSON hourly weather forecast for 7 days. Time always starts at 0:00 today and contains 168 hours. All URL parameters are listed below:\n\nParameter\tFormat\tRequired\tDefault\tDescription\nlatitude, longitude\tFloating point\tYes\t\tGeographical WGS84 coordinate of the location\nhourly\tString array\tNo\t\tA list of weather variables which should be returned. Values can be comma separated, or multiple &hourly= parameter in the URL can be used.\ndaily\tString array\tNo\t\tA list of daily weather variable aggregations which should be returned. Values can be comma separated, or multiple &daily= parameter in the URL can be used. If daily weather variables are specified, parameter timezone is required.\ncurrent_weather\tBool\tNo\tfalse\tInclude current weather conditions in the JSON output.\ntemperature_unit\tString\tNo\tcelsius\tIf fahrenheit is set, all temperature values are converted to Fahrenheit.\nwindspeed_unit\tString\tNo\tkmh\tOther wind speed speed units: ms, mph and kn\nprecipitation_unit\tString\tNo\tmm\tOther precipitation amount units: inch\ntimeformat\tString\tNo\tiso8601\tIf format unixtime is selected, all time values are returned in UNIX epoch time in seconds. Please note that all timestamp are in GMT+0! For daily values with unix timestamps, please apply utc_offset_seconds again to get the correct date.\ntimezone\tString\tNo\tGMT\tIf timezone is set, all timestamps are returned as local-time and data is returned starting at 00:00 local-time. Any time zone name from the time zone database is supported. If auto is set as a time zone, the coordinates will be automatically resolved to the local time zone.\npast_days\tInteger (0-2)\tNo\t0\tIf past_days is set, yesterday or the day before yesterday data are also returned.\nstart_date\nend_date\tString (yyyy-mm-dd)\tNo\t\tThe time interval to get weather data. A day must be specified as an ISO8601 date (e.g. 2022-06-30).\nmodels\tString array\tNo\tauto\tManually select one or more weather models. Per default, the best suitable weather models will be combined.\n\nHourly Parameter Definition\nThe parameter &hourly= accepts the following values. Most weather variables are given as an instantaneous value for the indicated hour. Some variables like precipitation are calculated from the preceding hour as an average or sum.\n\nVariable\tValid time\tUnit\tDescription\ntemperature_2m\tInstant\t°C (°F)\tAir temperature at 2 meters above ground\nsnowfall\tPreceding hour sum\tcm (inch)\tSnowfall amount of the preceding hour in centimeters. For the water equivalent in millimeter, divide by 7. E.g. 7 cm snow = 10 mm precipitation water equivalent\nrain\tPreceding hour sum\tmm (inch)\tRain from large scale weather systems of the preceding hour in millimeter\nshowers\tPreceding hour sum\tmm (inch)\tShowers from convective precipitation in millimeters from the preceding hour\nweathercode\tInstant\tWMO code\tWeather condition as a numeric code. Follow WMO weather interpretation codes. See table below for details.\nsnow_depth\tInstant\tmeters\tSnow depth on the ground\nfreezinglevel_height\tInstant\tmeters\tAltitude above sea level of the 0°C level\nvisibility\tInstant\tmeters\tViewing distance in meters. Influenced by low clouds, humidity and aerosols. Maximum visibility is approximately 24 km.", "headers": "", "urlPrompt": "You are given the below API Documentation:\n{api_docs}\nUsing this documentation, generate the full API url to call for answering the user question.\nYou should build the API url in order to get a response that is as short as possible, while still getting the necessary information to answer the question. Pay attention to deliberately exclude any unnecessary pieces of data in the API call.\n\nQuestion:{question}\nAPI url:", "ansPrompt": "Given this {api_response} response for {api_url}. use the given response to answer this {question}"}, "outputAnchors": [{"id": "getApiChain_0-output-getApiChain-GETApiChain|BaseChain|BaseLangChain", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "GETApiChain", "type": "GETApiChain | BaseChain | BaseLangChain"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1222.6923202234623, "y": 359.97676456347756}, "dragging": false}, {"width": 300, "height": 603, "id": "chainTool_0", "position": {"x": 1600.1485877701232, "y": 276.38970893436533}, "type": "customNode", "data": {"id": "chainTool_0", "label": "Chain Tool", "version": 1, "name": "chainTool", "type": "ChainTool", "baseClasses": ["ChainTool", "DynamicTool", "Tool", "StructuredTool", "BaseLangChain"], "category": "Tools", "description": "Use a chain as allowed tool for agent", "inputParams": [{"label": "Chain Name", "name": "name", "type": "string", "placeholder": "state-of-union-qa", "id": "chainTool_0-input-name-string"}, {"label": "Chain Description", "name": "description", "type": "string", "rows": 3, "placeholder": "State of the Union QA - useful for when you need to ask questions about the most recent state of the union address.", "id": "chainTool_0-input-description-string"}, {"label": "Return Direct", "name": "returnDirect", "type": "boolean", "optional": true, "id": "chainTool_0-input-returnDirect-boolean"}], "inputAnchors": [{"label": "Base Chain", "name": "baseChain", "type": "BaseChain", "id": "chainTool_0-input-baseChain-BaseChain"}], "inputs": {"name": "weather-qa", "description": "useful for when you need to ask question about weather", "returnDirect": false, "baseChain": "{{getApiChain_0.data.instance}}"}, "outputAnchors": [{"id": "chainTool_0-output-chainTool-ChainTool|DynamicTool|Tool|StructuredTool|BaseLangChain", "name": "chainTool", "label": "ChainTool", "type": "ChainTool | DynamicTool | Tool | StructuredTool | BaseLangChain"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1600.1485877701232, "y": 276.38970893436533}, "dragging": false}, {"width": 300, "height": 253, "id": "bufferMemory_0", "position": {"x": 1642.0644080121785, "y": 1715.6131926891728}, "type": "customNode", "data": {"id": "bufferMemory_0", "label": "Buffer Memory", "version": 2, "name": "bufferMemory", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseChatMemory", "BaseMemory"], "category": "Memory", "description": "Retrieve chat messages stored in database", "inputParams": [{"label": "Session Id", "name": "sessionId", "type": "string", "description": "If not specified, a random id will be used. Learn <a target=\"_blank\" href=\"https://docs.flowiseai.com/memory#ui-and-embedded-chat\">more</a>", "default": "", "additionalParams": true, "optional": true, "id": "bufferMemory_0-input-sessionId-string"}, {"label": "Memory Key", "name": "<PERSON><PERSON><PERSON>", "type": "string", "default": "chat_history", "additionalParams": true, "id": "bufferMemory_0-input-memoryKey-string"}], "inputAnchors": [], "inputs": {"sessionId": "", "memoryKey": "chat_history"}, "outputAnchors": [{"id": "bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "name": "bufferMemory", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "BufferMemory | BaseChatMemory | BaseMemory"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1642.0644080121785, "y": 1715.6131926891728}, "dragging": false}, {"width": 300, "height": 603, "id": "chainTool_1", "position": {"x": 1284.7746596034926, "y": 895.1444797047182}, "type": "customNode", "data": {"id": "chainTool_1", "label": "Chain Tool", "version": 1, "name": "chainTool", "type": "ChainTool", "baseClasses": ["ChainTool", "DynamicTool", "Tool", "StructuredTool", "BaseLangChain"], "category": "Tools", "description": "Use a chain as allowed tool for agent", "inputParams": [{"label": "Chain Name", "name": "name", "type": "string", "placeholder": "state-of-union-qa", "id": "chainTool_1-input-name-string"}, {"label": "Chain Description", "name": "description", "type": "string", "rows": 3, "placeholder": "State of the Union QA - useful for when you need to ask questions about the most recent state of the union address.", "id": "chainTool_1-input-description-string"}, {"label": "Return Direct", "name": "returnDirect", "type": "boolean", "optional": true, "id": "chainTool_1-input-returnDirect-boolean"}], "inputAnchors": [{"label": "Base Chain", "name": "baseChain", "type": "BaseChain", "id": "chainTool_1-input-baseChain-BaseChain"}], "inputs": {"name": "discord-bot", "description": "useful for when you need to send message to <PERSON>rd", "returnDirect": "", "baseChain": "{{postApiChain_0.data.instance}}"}, "outputAnchors": [{"id": "chainTool_1-output-chainTool-ChainTool|DynamicTool|Tool|StructuredTool|BaseLangChain", "name": "chainTool", "label": "ChainTool", "type": "ChainTool | DynamicTool | Tool | StructuredTool | BaseLangChain"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1284.7746596034926, "y": 895.1444797047182}, "dragging": false}, {"width": 300, "height": 460, "id": "postApiChain_0", "position": {"x": 933.3631140153886, "y": 974.8756002461283}, "type": "customNode", "data": {"id": "postApiChain_0", "label": "POST API Chain", "version": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "POSTApi<PERSON>hain", "baseClasses": ["POSTApi<PERSON>hain", "BaseChain", "BaseLangChain"], "category": "Chains", "description": "Chain to run queries against POST API", "inputParams": [{"label": "API Documentation", "name": "apiDocs", "type": "string", "description": "Description of how API works. Please refer to more <a target=\"_blank\" href=\"https://github.com/langchain-ai/langchain/blob/master/libs/langchain/langchain/chains/api/open_meteo_docs.py\">examples</a>", "rows": 4, "id": "postApiChain_0-input-apiDocs-string"}, {"label": "Headers", "name": "headers", "type": "json", "additionalParams": true, "optional": true, "id": "postApiChain_0-input-headers-json"}, {"label": "URL Prompt", "name": "urlPrompt", "type": "string", "description": "Prompt used to tell LLMs how to construct the URL. Must contains {api_docs} and {question}", "default": "You are given the below API Documentation:\n{api_docs}\nUsing this documentation, generate a json string with two keys: \"url\" and \"data\".\nThe value of \"url\" should be a string, which is the API url to call for answering the user question.\nThe value of \"data\" should be a dictionary of key-value pairs you want to POST to the url as a JSON body.\nBe careful to always use double quotes for strings in the json string.\nYou should build the json string in order to get a response that is as short as possible, while still getting the necessary information to answer the question. Pay attention to deliberately exclude any unnecessary pieces of data in the API call.\n\nQuestion:{question}\njson string:", "rows": 4, "additionalParams": true, "id": "postApiChain_0-input-urlPrompt-string"}, {"label": "Answer Prompt", "name": "ansPrompt", "type": "string", "description": "Prompt used to tell LLMs how to return the API response. Must contains {api_response}, {api_url}, and {question}", "default": "You are given the below API Documentation:\n{api_docs}\nUsing this documentation, generate a json string with two keys: \"url\" and \"data\".\nThe value of \"url\" should be a string, which is the API url to call for answering the user question.\nThe value of \"data\" should be a dictionary of key-value pairs you want to POST to the url as a JSON body.\nBe careful to always use double quotes for strings in the json string.\nYou should build the json string in order to get a response that is as short as possible, while still getting the necessary information to answer the question. Pay attention to deliberately exclude any unnecessary pieces of data in the API call.\n\nQuestion:{question}\njson string: {api_url_body}\n\nHere is the response from the API:\n\n{api_response}\n\nSummarize this response to answer the original question.\n\nSummary:", "rows": 4, "additionalParams": true, "id": "postApiChain_0-input-ansPrompt-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "postApiChain_0-input-model-BaseLanguageModel"}], "inputs": {"model": "{{chatOpenAI_2.data.instance}}", "apiDocs": "API documentation:\nEndpoint: https://some-discord-webhook.com\n\nThis API is for sending Discord message\n\nQuery body table:\nmessage | string | Message to send | required\n\nResponse schema (string):\nresult | string", "headers": "", "urlPrompt": "You are given the below API Documentation:\n{api_docs}\nUsing this documentation, generate a json string with two keys: \"url\" and \"data\".\nThe value of \"url\" should be a string, which is the API url to call for answering the user question.\nThe value of \"data\" should be a dictionary of key-value pairs you want to POST to the url as a JSON body.\nBe careful to always use double quotes for strings in the json string.\nYou should build the json string in order to get a response that is as short as possible, while still getting the necessary information to answer the question. Pay attention to deliberately exclude any unnecessary pieces of data in the API call.\n\nQuestion:{question}\njson string:", "ansPrompt": "You are given the below API Documentation:\n{api_docs}\nUsing this documentation, generate a json string with two keys: \"url\" and \"data\".\nThe value of \"url\" should be a string, which is the API url to call for answering the user question.\nThe value of \"data\" should be a dictionary of key-value pairs you want to POST to the url as a JSON body.\nBe careful to always use double quotes for strings in the json string.\nYou should build the json string in order to get a response that is as short as possible, while still getting the necessary information to answer the question. Pay attention to deliberately exclude any unnecessary pieces of data in the API call.\n\nQuestion:{question}\njson string: {api_url_body}\n\nHere is the response from the API:\n\n{api_response}\n\nSummarize this response to answer the original question.\n\nSummary:"}, "outputAnchors": [{"id": "postApiChain_0-output-postApiChain-POSTApiChain|BaseChain|BaseLangChain", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "POSTApi<PERSON>hain", "type": "POSTApiChain | BaseChain | BaseLangChain"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 933.3631140153886, "y": 974.8756002461283}, "dragging": false}, {"width": 300, "height": 670, "id": "chatOpenAI_2", "position": {"x": 572.8941615312035, "y": 937.8425220917356}, "type": "customNode", "data": {"id": "chatOpenAI_2", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_2-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_2-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "default": 0.9, "optional": true, "id": "chatOpenAI_2-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_2-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_2-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_2-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_2-input-cache-BaseCache"}], "inputs": {"modelName": "gpt-3.5-turbo", "temperature": 0.9, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_2-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 572.8941615312035, "y": 937.8425220917356}, "dragging": false}, {"width": 300, "height": 670, "id": "chatOpenAI_1", "position": {"x": 859.9597222599807, "y": 163.26344718821986}, "type": "customNode", "data": {"id": "chatOpenAI_1", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_1-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_1-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "default": 0.9, "optional": true, "id": "chatOpenAI_1-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_1-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_1-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_1-input-cache-BaseCache"}], "inputs": {"modelName": "gpt-3.5-turbo", "temperature": "0.6", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 859.9597222599807, "y": 163.26344718821986}, "dragging": false}, {"width": 300, "height": 670, "id": "chatOpenAI_3", "position": {"x": 1148.338912314111, "y": 1561.0888070167944}, "type": "customNode", "data": {"id": "chatOpenAI_3", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_3-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_3-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "default": 0.9, "optional": true, "id": "chatOpenAI_3-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_3-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_3-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_3-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_3-input-cache-BaseCache"}], "inputs": {"modelName": "gpt-3.5-turbo-16k", "temperature": 0.9, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_3-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1148.338912314111, "y": 1561.0888070167944}, "dragging": false}, {"id": "toolAgent_0", "position": {"x": 2087.462952706838, "y": 974.6001334100872}, "type": "customNode", "data": {"id": "toolAgent_0", "label": "Tool Agent", "version": 1, "name": "toolAgent", "type": "AgentExecutor", "baseClasses": ["AgentExecutor", "BaseChain", "Runnable"], "category": "Agents", "description": "Agent that uses Function Calling to pick the tools and args to call", "inputParams": [{"label": "System Message", "name": "systemMessage", "type": "string", "default": "You are a helpful AI assistant.", "rows": 4, "optional": true, "additionalParams": true, "id": "toolAgent_0-input-systemMessage-string"}, {"label": "Max Iterations", "name": "maxIterations", "type": "number", "optional": true, "additionalParams": true, "id": "toolAgent_0-input-maxIterations-number"}], "inputAnchors": [{"label": "Tools", "name": "tools", "type": "Tool", "list": true, "id": "toolAgent_0-input-tools-Tool"}, {"label": "Memory", "name": "memory", "type": "BaseChatMemory", "id": "toolAgent_0-input-memory-BaseChatMemory"}, {"label": "Tool Calling Chat Model", "name": "model", "type": "BaseChatModel", "description": "Only compatible with models that are capable of function calling: ChatOpenAI, ChatMistral, ChatAnthropic, ChatGoogleGenerativeAI, ChatVertexAI, GroqChat", "id": "toolAgent_0-input-model-BaseChatModel"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "toolAgent_0-input-inputModeration-Moderation"}], "inputs": {"tools": ["{{chainTool_0.data.instance}}", "{{chainTool_1.data.instance}}"], "memory": "{{bufferMemory_0.data.instance}}", "model": "{{chatOpenAI_3.data.instance}}", "systemMessage": "You are a helpful AI assistant.", "inputModeration": "", "maxIterations": ""}, "outputAnchors": [{"id": "toolAgent_0-output-toolAgent-AgentExecutor|BaseChain|Runnable", "name": "toolAgent", "label": "AgentExecutor", "description": "Agent that uses Function Calling to pick the tools and args to call", "type": "AgentExecutor | BaseChain | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 435, "selected": false, "positionAbsolute": {"x": 2087.462952706838, "y": 974.6001334100872}, "dragging": false}, {"id": "stickyNote_0", "position": {"x": 2081.8371244608006, "y": 595.924073574161}, "type": "stickyNote", "data": {"id": "stickyNote_0", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_0-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Using agent, we give it 2 tools that is each attached to a GET/POST API Chain.\n\nThe goal is to have the agent to decide when to use which tool. \n\nWhen the tool is being used, API Chain's task is to figure out the correct URL and params to make the HTTP call.\n\nHowever, it is recommended to use OpenAPI YML to give a more structured input to LLM, for better quality output.\n\nExample question:\nSend me the weather of SF today to my discord"}, "outputAnchors": [{"id": "stickyNote_0-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 364, "selected": false, "positionAbsolute": {"x": 2081.8371244608006, "y": 595.924073574161}, "dragging": false}], "edges": [{"source": "getApiChain_0", "sourceHandle": "getApiChain_0-output-getApiChain-GETApiChain|BaseChain|BaseLangChain", "target": "chainTool_0", "targetHandle": "chainTool_0-input-baseChain-BaseChain", "type": "buttonedge", "id": "getApiChain_0-getApiChain_0-output-getApiChain-GETApiChain|BaseChain|BaseLangChain-chainTool_0-chainTool_0-input-baseChain-BaseChain", "data": {"label": ""}}, {"source": "postApiChain_0", "sourceHandle": "postApiChain_0-output-postApiChain-POSTApiChain|BaseChain|BaseLangChain", "target": "chainTool_1", "targetHandle": "chainTool_1-input-baseChain-BaseChain", "type": "buttonedge", "id": "postApiChain_0-postApiChain_0-output-postApiChain-POSTApiChain|BaseChain|BaseLangChain-chainTool_1-chainTool_1-input-baseChain-BaseChain", "data": {"label": ""}}, {"source": "chatOpenAI_2", "sourceHandle": "chatOpenAI_2-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel", "target": "postApiChain_0", "targetHandle": "postApiChain_0-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_2-chatOpenAI_2-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel-postApiChain_0-postApiChain_0-input-model-BaseLanguageModel", "data": {"label": ""}}, {"source": "chatOpenAI_1", "sourceHandle": "chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel", "target": "getApiChain_0", "targetHandle": "getApiChain_0-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_1-chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel-getApiChain_0-getApiChain_0-input-model-BaseLanguageModel", "data": {"label": ""}}, {"source": "chainTool_0", "sourceHandle": "chainTool_0-output-chainTool-ChainTool|DynamicTool|Tool|StructuredTool|BaseLangChain", "target": "toolAgent_0", "targetHandle": "toolAgent_0-input-tools-Tool", "type": "buttonedge", "id": "chainTool_0-chainTool_0-output-chainTool-ChainTool|DynamicTool|Tool|StructuredTool|BaseLangChain-toolAgent_0-toolAgent_0-input-tools-Tool"}, {"source": "chainTool_1", "sourceHandle": "chainTool_1-output-chainTool-ChainTool|DynamicTool|Tool|StructuredTool|BaseLangChain", "target": "toolAgent_0", "targetHandle": "toolAgent_0-input-tools-Tool", "type": "buttonedge", "id": "chainTool_1-chainTool_1-output-chainTool-ChainTool|DynamicTool|Tool|StructuredTool|BaseLangChain-toolAgent_0-toolAgent_0-input-tools-Tool"}, {"source": "bufferMemory_0", "sourceHandle": "bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "target": "toolAgent_0", "targetHandle": "toolAgent_0-input-memory-BaseChatMemory", "type": "buttonedge", "id": "bufferMemory_0-bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory-toolAgent_0-toolAgent_0-input-memory-BaseChatMemory"}, {"source": "chatOpenAI_3", "sourceHandle": "chatOpenAI_3-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel", "target": "toolAgent_0", "targetHandle": "toolAgent_0-input-model-BaseChatModel", "type": "buttonedge", "id": "chatOpenAI_3-chatOpenAI_3-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel-toolAgent_0-toolAgent_0-input-model-BaseChatModel"}]}