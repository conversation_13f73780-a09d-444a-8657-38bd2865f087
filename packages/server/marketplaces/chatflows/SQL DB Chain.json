{"description": "Answer questions over a SQL database", "usecases": ["SQL"], "framework": ["Langchain"], "nodes": [{"width": 300, "height": 522, "id": "chatOpenAI_0", "position": {"x": 855.0396169649254, "y": 179.29430548099504}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6.0, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"modelName": "gpt-3.5-turbo", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 855.0396169649254, "y": 179.29430548099504}, "dragging": false}, {"width": 300, "height": 475, "id": "sqlDatabaseChain_0", "position": {"x": 1206.5244299447634, "y": 201.04431101230608}, "type": "customNode", "data": {"id": "sqlDatabaseChain_0", "label": "Sql Database Chain", "version": 5, "name": "sqlDatabaseChain", "type": "SqlDatabaseChain", "baseClasses": ["SqlDatabaseChain", "BaseChain", "Runnable"], "category": "Chains", "description": "Answer questions over a SQL database", "inputParams": [{"label": "Database", "name": "database", "type": "options", "options": [{"label": "SQLite", "name": "sqlite"}, {"label": "PostgreSQL", "name": "postgres"}, {"label": "MSSQL", "name": "mssql"}, {"label": "MySQL", "name": "mysql"}], "default": "sqlite", "id": "sqlDatabaseChain_0-input-database-options"}, {"label": "Connection string or file path (sqlite only)", "name": "url", "type": "string", "placeholder": "127.0.0.1:5432/chinook", "id": "sqlDatabaseChain_0-input-url-string"}, {"label": "Include Tables", "name": "includesTables", "type": "string", "description": "Tables to include for queries, seperated by comma. Can only use Include Tables or Ignore Tables", "placeholder": "table1, table2", "additionalParams": true, "optional": true, "id": "sqlDatabaseChain_0-input-includesTables-string"}, {"label": "Ignore Tables", "name": "ignoreTables", "type": "string", "description": "Tables to ignore for queries, seperated by comma. Can only use Ignore Tables or Include Tables", "placeholder": "table1, table2", "additionalParams": true, "optional": true, "id": "sqlDatabaseChain_0-input-ignoreTables-string"}, {"label": "Sample table's rows info", "name": "sampleRowsInTableInfo", "type": "number", "description": "Number of sample row for tables to load for info.", "placeholder": "3", "additionalParams": true, "optional": true, "id": "sqlDatabaseChain_0-input-sampleRowsInTableInfo-number"}, {"label": "Top Keys", "name": "topK", "type": "number", "description": "If you are querying for several rows of a table you can select the maximum number of results you want to get by using the top_k parameter (default is 10). This is useful for avoiding query results that exceed the prompt max length or consume tokens unnecessarily.", "placeholder": "10", "additionalParams": true, "optional": true, "id": "sqlDatabaseChain_0-input-topK-number"}, {"label": "Custom Prompt", "name": "customPrompt", "type": "string", "description": "You can provide custom prompt to the chain. This will override the existing default prompt used. See <a target=\"_blank\" href=\"https://python.langchain.com/docs/integrations/tools/sqlite#customize-prompt\">guide</a>", "warning": "Prompt must include 3 input variables: {input}, {dialect}, {table_info}. You can refer to official guide from description above", "rows": 4, "placeholder": "Given an input question, first create a syntactically correct {dialect} query to run, then look at the results of the query and return the answer. Unless the user specifies in his question a specific number of examples he wishes to obtain, always limit your query to at most {top_k} results. You can order the results by a relevant column to return the most interesting examples in the database.\n\nNever query for all the columns from a specific table, only ask for a the few relevant columns given the question.\n\nPay attention to use only the column names that you can see in the schema description. Be careful to not query for columns that do not exist. Also, pay attention to which column is in which table.\n\nUse the following format:\n\nQuestion: \"Question here\"\nSQLQuery: \"SQL Query to run\"\nSQLResult: \"Result of the SQLQuery\"\nAnswer: \"Final answer here\"\n\nOnly use the tables listed below.\n\n{table_info}\n\nQuestion: {input}", "additionalParams": true, "optional": true, "id": "sqlDatabaseChain_0-input-customPrompt-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "sqlDatabaseChain_0-input-model-BaseLanguageModel"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "sqlDatabaseChain_0-input-inputModeration-Moderation"}], "inputs": {"inputModeration": "", "model": "{{chatOpenAI_0.data.instance}}", "database": "sqlite", "url": "", "customPrompt": ""}, "outputAnchors": [{"id": "sqlDatabaseChain_0-output-sqlDatabaseChain-SqlDatabaseChain|BaseChain|Runnable", "name": "sqlDatabaseChain", "label": "SqlDatabaseChain", "type": "SqlDatabaseChain | BaseChain | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1206.5244299447634, "y": 201.04431101230608}, "dragging": false}], "edges": [{"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel", "target": "sqlDatabaseChain_0", "targetHandle": "sqlDatabaseChain_0-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel-sqlDatabaseChain_0-sqlDatabaseChain_0-input-model-BaseLanguageModel", "data": {"label": ""}}]}