{"description": "Simple LLM Chain using HuggingFace Inference API on falcon-7b-instruct model", "framework": ["Langchain"], "usecases": ["Basic"], "nodes": [{"width": 300, "height": 475, "id": "promptTemplate_0", "position": {"x": 506.50436294210306, "y": 504.50766458127396}, "type": "customNode", "data": {"id": "promptTemplate_0", "label": "Prompt Template", "version": 1, "name": "promptTemplate", "type": "PromptTemplate", "baseClasses": ["PromptTemplate", "BaseStringPromptTemplate", "BasePromptTemplate"], "category": "Prompts", "description": "Schema to represent a basic prompt for an LLM", "inputParams": [{"label": "Template", "name": "template", "type": "string", "rows": 4, "placeholder": "What is a good name for a company that makes {product}?", "id": "promptTemplate_0-input-template-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "promptTemplate_0-input-promptValues-json"}], "inputAnchors": [], "inputs": {"template": "Question: {question}\n\nAnswer: Let's think step by step.", "promptValues": ""}, "outputAnchors": [{"id": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate", "name": "promptTemplate", "label": "PromptTemplate", "type": "PromptTemplate | BaseStringPromptTemplate | BasePromptTemplate"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 506.50436294210306, "y": 504.50766458127396}, "dragging": false}, {"width": 300, "height": 577, "id": "huggingFaceInference_LLMs_0", "position": {"x": 498.8594464193537, "y": -94.91050256311678}, "type": "customNode", "data": {"id": "huggingFaceInference_LLMs_0", "label": "HuggingFace Inference", "version": 2, "name": "huggingFaceInference_LLMs", "type": "HuggingFaceInference", "baseClasses": ["HuggingFaceInference", "LLM", "BaseLLM", "BaseLanguageModel"], "category": "LLMs", "description": "Wrapper around HuggingFace large language models", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["huggingFace<PERSON>pi"], "id": "huggingFaceInference_LLMs_0-input-credential-credential"}, {"label": "Model", "name": "model", "type": "string", "description": "If using own inference endpoint, leave this blank", "placeholder": "gpt2", "optional": true, "id": "huggingFaceInference_LLMs_0-input-model-string"}, {"label": "Endpoint", "name": "endpoint", "type": "string", "placeholder": "https://xyz.eu-west-1.aws.endpoints.huggingface.cloud/gpt2", "description": "Using your own inference endpoint", "optional": true, "id": "huggingFaceInference_LLMs_0-input-endpoint-string"}, {"label": "Temperature", "name": "temperature", "type": "number", "description": "Temperature parameter may not apply to certain model. Please check available model parameters", "optional": true, "additionalParams": true, "id": "huggingFaceInference_LLMs_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "description": "Max Tokens parameter may not apply to certain model. Please check available model parameters", "optional": true, "additionalParams": true, "id": "huggingFaceInference_LLMs_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "description": "Top Probability parameter may not apply to certain model. Please check available model parameters", "optional": true, "additionalParams": true, "id": "huggingFaceInference_LLMs_0-input-topP-number"}, {"label": "Top K", "name": "hfTopK", "type": "number", "description": "Top K parameter may not apply to certain model. Please check available model parameters", "optional": true, "additionalParams": true, "id": "huggingFaceInference_LLMs_0-input-hfTopK-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "description": "Frequency Penalty parameter may not apply to certain model. Please check available model parameters", "optional": true, "additionalParams": true, "id": "huggingFaceInference_LLMs_0-input-frequencyPenalty-number"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "huggingFaceInference_LLMs_0-input-cache-BaseCache"}], "inputs": {"model": "tiiuae/falcon-7b-instruct", "endpoint": "", "temperature": "", "maxTokens": "", "topP": "", "hfTopK": "", "frequencyPenalty": ""}, "outputAnchors": [{"id": "huggingFaceInference_LLMs_0-output-huggingFaceInference_LLMs-HuggingFaceInference|LLM|BaseLLM|BaseLanguageModel", "name": "huggingFaceInference_LLMs", "label": "HuggingFaceInference", "type": "HuggingFaceInference | LLM | BaseLLM | BaseLanguageModel"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 498.8594464193537, "y": -94.91050256311678}, "dragging": false}, {"width": 300, "height": 456, "id": "llmChain_0", "position": {"x": 909.6249320819859, "y": 338.9520801783737}, "type": "customNode", "data": {"id": "llmChain_0", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_0-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_0-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_0-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_0-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_0-input-inputModeration-Moderation"}], "inputs": {"model": "{{huggingFaceInference_LLMs_0.data.instance}}", "prompt": "{{promptTemplate_0.data.instance}}", "outputParser": "", "chainName": "", "inputModeration": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_0-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_0-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 909.6249320819859, "y": 338.9520801783737}, "dragging": false}], "edges": [{"source": "huggingFaceInference_LLMs_0", "sourceHandle": "huggingFaceInference_LLMs_0-output-huggingFaceInference_LLMs-HuggingFaceInference|LLM|BaseLLM|BaseLanguageModel", "target": "llmChain_0", "targetHandle": "llmChain_0-input-model-BaseLanguageModel", "type": "buttonedge", "id": "huggingFaceInference_LLMs_0-huggingFaceInference_LLMs_0-output-huggingFaceInference_LLMs-HuggingFaceInference|LLM|BaseLLM|BaseLanguageModel-llmChain_0-llmChain_0-input-model-BaseLanguageModel", "data": {"label": ""}}, {"source": "promptTemplate_0", "sourceHandle": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate", "target": "llmChain_0", "targetHandle": "llmChain_0-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "promptTemplate_0-promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate-llmChain_0-llmChain_0-input-prompt-BasePromptTemplate", "data": {"label": ""}}]}