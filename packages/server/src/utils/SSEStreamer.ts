import express, { Response } from 'express'
import { IServerSideEventStreamer } from 'flowise-components'

// define a new type that has a client type (INTERNAL or EXTERNAL) and Response type
type Client = {
  // future use
  clientType: 'INTERNAL' | 'EXTERNAL'
  response: Response
  // optional property with default value
  started?: boolean
}

export class SSEStreamer implements IServerSideEventStreamer {
  clients: { [id: string]: Client } = {}
  app: express.Application

  constructor(app: express.Application) {
    this.app = app
  }

  addExternalClient(chatId: string, res: Response) {
    this.clients[chatId] = { clientType: 'EXTERNAL', response: res, started: false }
  }

  addClient(chatId: string, res: Response) {
    this.clients[chatId] = { clientType: 'INTERNAL', response: res, started: false }
  }

  removeClient(chatId: string) {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'end',
        data: '[DONE]'
      }
      client.response.write('message\ndata:' + JSO<PERSON>.stringify(clientResponse) + '\n\n')
      client.response.end()
      delete this.clients[chatId]
    }
  }

  // Send SSE message to a specific client
  streamEvent(chatId: string, data: string) {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'start',
        data: data
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }

  streamRecheck(chatId: string, data: string) {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'recheck',
        data: data
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }

  streamCustomEvent(chatId: string, eventType: string, data: any) {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: eventType,
        data: data
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }

  streamStartEvent(chatId: string, data: string) {
    const client = this.clients[chatId]
    // prevent multiple start events being streamed to the client
    if (client && !client.started) {
      const clientResponse = {
        event: 'start',
        data: data
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
      client.started = true
    }
  }

  streamTokenEvent(chatId: string, data: string) {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'token',
        data: data
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }

  streamSourceDocumentsEvent(chatId: string, data: any) {
    const client = this.clients[chatId]
    if (client) {
      // Create a set to track PDF sources we've seen so far.
      const seenPdfSources = new Set<string>()
      const seenDocxOrXlsxs = new Set<string>()

      const filteredDocs = data
        .filter((doc: any) => {
          if (doc?.metadata?.source?.endsWith('.txt')) return true

          // If this is a PDF document, check if its source has already been seen.
          if (doc?.metadata?.source?.endsWith('.pdf')) {
            if (seenPdfSources.has(doc.metadata.source)) {
              return false // Already seen, skip this duplicate.
            }
            seenPdfSources.add(doc.metadata.source)
            return true
          }

          if (doc?.metadata?.source?.endsWith('.docx') || doc?.metadata?.source?.endsWith('.xlsx')) {
            if (seenDocxOrXlsxs.has(doc.metadata.source)) {
              return false
            }
            seenDocxOrXlsxs.add(doc.metadata.source)
            return true
          }

          // For non-PDF documents, include them if they have a URL.
          return !!doc?.metadata?.url
        })
        .map((doc: any) => {
          const allowExts = ['.pdf', 'txt', 'docx', 'xlsx']

          for (const allowExt of allowExts) {
            if (doc?.metadata?.source?.endsWith(allowExt)) {
              return doc
            }
          }

          // For non-PDF documents, set additional metadata properties.
          doc.metadata['source'] = doc.metadata.url
          doc.metadata['x-amz-bedrock-kb-source-uri'] = doc.metadata.url

          delete doc.metadata['pageContent']

          return doc
        })

      const clientResponse = {
        event: 'sourceDocuments',
        data: filteredDocs
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }

  streamArtifactsEvent(chatId: string, data: any) {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'artifacts',
        data: data
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }
  streamUsedToolsEvent(chatId: string, data: any): void {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'usedTools',
        data: data
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }
  streamFileAnnotationsEvent(chatId: string, data: any): void {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'fileAnnotations',
        data: data
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }
  streamToolEvent(chatId: string, data: any): void {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'tool',
        data: data
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }
  streamAgentReasoningEvent(chatId: string, data: any): void {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'agentReasoning',
        data: data
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }
  streamNextAgentEvent(chatId: string, data: any): void {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'nextAgent',
        data: data
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }
  streamActionEvent(chatId: string, data: any): void {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'action',
        data: data
      }
      client.response.write('message:\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }

  streamAbortEvent(chatId: string): void {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'abort',
        data: '[DONE]'
      }
      client.response.write('message\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }

  streamEndEvent(_: string) {
    // placeholder for future use
  }

  streamErrorEvent(chatId: string, msg: string) {
    const client = this.clients[chatId]
    if (client) {
      const clientResponse = {
        event: 'error',
        data: msg
        //         data: `Agent đang tạm dừng dịch vụ để nâng cấp lên phiên bản mới. Vui lòng quay lại sau 30 phút nữa.
        // Cảm ơn bạn đã thông cảm và kiên nhẫn chờ đợi.`
      }
      client.response.write('message\ndata:' + JSON.stringify(clientResponse) + '\n\n')
    }
  }

  streamMetadataEvent(chatId: string, apiResponse: any) {
    const metadataJson: any = {}
    if (apiResponse.chatId) {
      metadataJson['chatId'] = apiResponse.chatId
    }
    if (apiResponse.chatMessageId) {
      metadataJson['chatMessageId'] = apiResponse.chatMessageId
    }
    if (apiResponse.question) {
      metadataJson['question'] = apiResponse.question
    }
    if (apiResponse.sessionId) {
      metadataJson['sessionId'] = apiResponse.sessionId
    }
    if (apiResponse.memoryType) {
      metadataJson['memoryType'] = apiResponse.memoryType
    }
    if (apiResponse.followUpPrompts) {
      metadataJson['followUpPrompts'] = JSON.parse(apiResponse.followUpPrompts)
    }
    if (Object.keys(metadataJson).length > 0) {
      this.streamCustomEvent(chatId, 'metadata', metadataJson)
    }
  }
}
