import { ChatMessageFeedback } from '../database/entities/ChatMessageFeedback'
import { IChatMessageFeedback } from '../Interface'
import { getRunningExpressApp } from '../utils/getRunningExpressApp'

/**
 * Method that add chat message feedback.
 * @param {Partial<IChatMessageFeedback>} chatMessageFeedback
 */

export const utilAddChatMessageFeedback = async (chatMessageFeedback: Partial<IChatMessageFeedback>) => {
  try {
    const appServer = getRunningExpressApp()
    const repository = appServer.AppDataSource.getRepository(ChatMessageFeedback)

    // Check if a record with the same messageId already exists
    const existingFeedback = await repository.findOneBy({ messageId: chatMessageFeedback.messageId })

    if (existingFeedback) {
      // Update the existing record
      Object.assign(existingFeedback, chatMessageFeedback)
      console.log('🚀 ~ addChatMessageFeedback.ts:15 ~ utilAddChatMessageFeedback ~ updating feedback:', existingFeedback)
      return await repository.save(existingFeedback)
    } else {
      // Create a new record
      const newChatMessageFeedback = new ChatMessageFeedback()
      Object.assign(newChatMessageFeedback, chatMessageFeedback)
      const feedback = repository.create(newChatMessageFeedback)
      console.log('🚀 ~ addChatMessageFeedback.ts:21 ~ utilAddChatMessageFeedback ~ creating feedback:', feedback)
      return await repository.save(feedback)
    }
  } catch (error) {
    console.log('🚀 ~ addChatMessageFeedback.ts:25 ~ utilAddChatMessageFeedback ~ error:', error)
  }
}
