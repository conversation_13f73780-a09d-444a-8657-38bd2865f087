import { Request, Response, NextFunction } from 'express'
import { StatusCodes } from 'http-status-codes'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import microsoftOAuthService from '../../services/microsoftOAuth'

// Login with Microsoft OAuth
const loginWithMicrosoft = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log('🔐 [Microsoft OAuth Controller]: Received Microsoft OAuth login request')
    
    const { accessToken } = req.body

    if (!accessToken) {
      console.error('🔐 [Microsoft OAuth Controller]: Missing access token in request')
      throw new InternalFlowiseError(StatusCodes.BAD_REQUEST, 'Access token is required')
    }

    console.log('🔐 [Microsoft OAuth Controller]: Processing Microsoft OAuth login', {
      tokenLength: accessToken.length,
      tokenPreview: accessToken.substring(0, 20) + '...'
    })

    const result = await microsoftOAuthService.loginWithMicrosoft(accessToken)

    console.log('🔐 [Microsoft OAuth Controller]: Microsoft OAuth login successful', {
      userId: result.user.id,
      username: result.user.username,
      hasAccessToken: !!result.accessToken,
      hasRefreshToken: !!result.refreshToken
    })

    return res.status(StatusCodes.OK).json({
      success: true,
      user: {
        id: result.user.id,
        username: result.user.username,
        email: result.user.email,
        displayName: result.user.displayName,
        role: result.user.role,
        groupname: result.user.groupname,
        active: result.user.active
      },
      accessToken: result.accessToken,
      refreshToken: result.refreshToken
    })
  } catch (error: any) {
    console.error('🔐 [Microsoft OAuth Controller]: Microsoft OAuth login failed', {
      error: error.message,
      statusCode: error.statusCode
    })
    next(error)
  }
}

export default {
  loginWithMicrosoft
}
