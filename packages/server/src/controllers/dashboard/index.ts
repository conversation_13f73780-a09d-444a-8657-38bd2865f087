import { NextFunction, Request, Response } from 'express'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import { QA } from '../../database/entities/AQ'

// [
//   {
//     id: 3,
//     name: 'chatid',
//     message: [
//       {
//         id: 4,
//         isFAQ: false,
//         name: '',
//         attachments: [],
//         question: 'Question 4',
//         answer: 'Answer 4',
//         questionTtype: ''
//       },
//       {
//         id: 5,
//         isFAQ: false,
//         name: '',
//         attachments: [],
//         question: 'Question 5',
//         answer: 'Answer 5',
//         questionTtype: ''
//       }
//     ]
//   }
// ]

export const getChatHistory = async (req: Request, res: Response, next: NextFunction) => {
  const page = parseInt(req.query.page as string) || 1
  const limit = parseInt(req.query.limit as string) || 10
  const flowId = req.query.flowId as string
  const start = (page - 1) * limit

  const appServer = getRunningExpressApp()
  const chatMessageRepo = appServer.AppDataSource.getRepository(QA)

  const [foundQa_list, total] = await chatMessageRepo
    .createQueryBuilder('qa')
    .where('qa.chatflowid = :flowId', { flowId })
    .orderBy('qa.createdDate', 'DESC')
    .skip(start)
    .take(limit)
    .getManyAndCount()

  const groupedData = foundQa_list.reduce((acc: any, qa) => {
    const existingChat = acc.find((chat: any) => chat?.id === qa.chatId)
    const message = {
      id: qa.id,
      isFAQ: false,
      name: '',
      attachments: [],
      question: qa.question,
      answer: qa.answer,
      questionTtype: qa.question_type || '',
      chatId: qa.chatId,
      createdDate: qa.createdDate,
      updatedDate: qa.updatedDate,
      beingProcessed: qa.being_processed,
      timeLastCron: qa.timeLastCron,
      userName: qa.userName,
      userId: qa.userId
    }

    if (existingChat) {
      existingChat.message.push(message)
    } else {
      acc.push({
        id: qa.chatId,
        name: '',
        message: [message]
      })
    }

    return acc
  }, [])

  const formattedData = groupedData

  res.json({
    data: formattedData,
    page,
    limit,
    total
  })
}
