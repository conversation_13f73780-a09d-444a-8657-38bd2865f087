import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddMicros<PERSON>Fields1700000000000 implements MigrationInterface {
  name = 'AddMicrosoftFields1700000000000'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if columns already exist
    const hasMicrosoftId = await queryRunner.hasColumn('users', 'microsoft_id')
    const hasDisplayName = await queryRunner.hasColumn('users', 'display_name')

    if (!hasMicrosoftId) {
      await queryRunner.query(`ALTER TABLE "users" ADD "microsoft_id" character varying(255)`)
    }

    if (!hasDisplayName) {
      await queryRunner.query(`ALTER TABLE "users" ADD "display_name" character varying(255)`)
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "microsoft_id"`)
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "display_name"`)
  }
} 