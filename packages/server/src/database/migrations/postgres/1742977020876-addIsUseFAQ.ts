import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddIsUseFAQ1742977020876 implements MigrationInterface {
  name = 'AddIsUseFAQ1742977020876'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_flow" ADD "isUseFAQ" boolean`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_flow" DROP COLUMN "isUseFAQ"`)
  }
}
