import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm'

export class AddMicrosoftOAuthFields1750200000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if microsoftId column exists
    const microsoftIdExists = await queryRunner.hasColumn('users', 'microsoftId')
    if (!microsoftIdExists) {
      await queryRunner.addColumn(
        'users',
        new TableColumn({
          name: 'microsoftId',
          type: 'varchar',
          length: '255',
          isNullable: true
        })
      )
    }

    // Check if displayName column exists
    const displayNameExists = await queryRunner.hasColumn('users', 'displayName')
    if (!displayNameExists) {
      await queryRunner.addColumn(
        'users',
        new TableColumn({
          name: 'displayName',
          type: 'varchar',
          length: '255',
          isNullable: true
        })
      )
    }

    // Create index on microsoftId for faster lookups if it doesn't exist
    const indexExists = await queryRunner.hasIndex('users', 'IDX_USER_MICROSOFT_ID')
    if (!indexExists) {
      await queryRunner.query(`CREATE INDEX "IDX_USER_MICROSOFT_ID" ON "users" ("microsoftId")`)
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop index if exists
    const indexExists = await queryRunner.hasIndex('users', 'IDX_USER_MICROSOFT_ID')
    if (indexExists) {
      await queryRunner.query(`DROP INDEX "IDX_USER_MICROSOFT_ID"`)
    }

    // Drop columns if they exist
    const displayNameExists = await queryRunner.hasColumn('users', 'displayName')
    if (displayNameExists) {
      await queryRunner.dropColumn('users', 'displayName')
    }

    const microsoftIdExists = await queryRunner.hasColumn('users', 'microsoftId')
    if (microsoftIdExists) {
      await queryRunner.dropColumn('users', 'microsoftId')
    }
  }
}
