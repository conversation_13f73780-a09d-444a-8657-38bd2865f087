import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddMicrosoft<PERSON>uthFields1703891895164 implements MigrationInterface {
  name = 'AddMicrosoftOAuthFields1703891895164'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add Microsoft OAuth fields to users table
    await queryRunner.query(`ALTER TABLE "users" ADD "microsoftId" character varying(255)`)
    await queryRunner.query(`ALTER TABLE "users" ADD "displayName" character varying(255)`)
    
    // Create index for microsoftId for faster lookups
    await queryRunner.query(`CREATE INDEX "IDX_USER_MICROSOFT_ID" ON "users" ("microsoftId")`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove index
    await queryRunner.query(`DROP INDEX "IDX_USER_MICROSOFT_ID"`)
    
    // Remove Microsoft OAuth fields
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "displayName"`)
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "microsoftId"`)
  }
}
