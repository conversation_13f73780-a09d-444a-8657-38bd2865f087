import { Entity, Column, CreateDate<PERSON>olumn, PrimaryGeneratedColumn, Index, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm'
import { ChatFlow } from './ChatFlow'
import { OneToMany } from 'typeorm'
import { GroupUsers } from './GroupUser'
import { ChatMessage } from './ChatMessage'

export enum UserRole {
  MASTER_ADMIN = 'MASTER_ADMIN',
  SITE_ADMIN = 'SITE_ADMIN',
  ADMIN = 'ADMIN',
  USER = 'USER'
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ type: 'varchar', length: 255, unique: true })
  username: string

  @Index()
  @Column({ type: 'varchar', length: 255, nullable: true, unique: true })
  email: string

  @Column({ type: 'bool', nullable: true, default: true })
  active: boolean

  @Column({ type: 'enum', enum: UserRole, default: UserRole.USER })
  role: UserRole

  @Column('varchar', { nullable: true, default: '' })
  groupname: string

  @Column('varchar', { nullable: true })
  displayPrefixes: string

  @Column({ type: 'varchar', length: 255 })
  password: string

  @OneToMany(() => ChatFlow, (chatFlow) => chatFlow.user)
  chatFlows: ChatFlow[]

  @OneToMany(() => ChatMessage, (chatMessage) => chatMessage.user)
  message: ChatMessage[]

  @ManyToOne(() => GroupUsers, (group) => group.groupname, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'groupname', referencedColumnName: 'groupname' })
  group: GroupUsers

  @Column({ type: 'timestamp' })
  @CreateDateColumn()
  createdDate: Date

  @Column({ type: 'timestamp' })
  @UpdateDateColumn()
  updatedDate: Date
}
