import express, { Router } from 'express'
import faqsController from '../../controllers/faqs'

const router: Router = express.Router()

// CREATE
router.post('/', faqsController.saveFaq)
router.post('/importfaqs', faqsController.importFaqs)

// READ
router.get('/', faqsController.getAllFaqs)
// router.get('/:id/:chatflowId', faqsController.getFaqById)
router.get('/search/:chatflowId', faqsController.searchFaqs)
router.get('/listofflowusingfaq', faqsController.getListOfFlowUsingFAQ)

// VECTOR SEARCH
router.get('/vectorsearch/:chatflowId', faqsController.vectorSearchFaqs)

// UPDATE
router.put('/:id', faqsController.updateFaq)

// DELETE
router.delete('/deleteall/:chatflowId', faqsController.deleteAllFaqs)
router.delete('/delete/:id/:chatflowId', faqsController.deleteFaq)
router.delete('/deleteindex/:chatflowId', faqsController.deleteIndex)

// SETTINGS
router.post('/settings/:chatflowId', faqsController.updateSettings)
router.post('/cronjob/refinefaq', faqsController.cronJobrefineFaqFromHistory)
router.get('/list-classify-qa/:chatflowId', faqsController.getRecentQARecords)
router.post('/cronjob/classify', faqsController.fetchClassifyQARecords)

// LABELS
router.post('/label', faqsController.addOrUpdateLabel)
router.delete('/label/:id/:chatflowId', faqsController.removeLabel)

// Add new route
router.post('/generate-prompt', faqsController.generateSystemPrompt)

export default router
