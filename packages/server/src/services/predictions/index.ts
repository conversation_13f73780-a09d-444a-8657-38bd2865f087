import { Request } from 'express'
import { StatusCodes } from 'http-status-codes'
import { utilBuildChatflow, utilBuildFaq } from '../../utils/buildChatflow'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { getErrorMessage } from '../../errors/utils'
import { IncomingInput } from '../../Interface'
import { v4 as uuidv4 } from 'uuid'
import { ChatFlow } from '../../database/entities/ChatFlow'
import { User } from '../../database/entities/User'

const buildChatflow = async (fullRequest: Request, chatflow: ChatFlow | null = null, user: User) => {
  try {
    const dbResponse = await utilBuildChatflow(fullRequest, false, chatflow, user)
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: predictionsServices.buildChatflow - ${getErrorMessage(error)}`
    )
  }
}

const buildFaq = async (fullRequest: any, chatflow: ChatFlow | null = null, user: User) => {
  try {
    const { faqId } = fullRequest.body
    const chatflowid = fullRequest.params.id

    let incomingInput: IncomingInput = fullRequest.body

    const chatId = incomingInput.chatId ?? incomingInput.overrideConfig?.sessionId ?? uuidv4()

    if (!faqId || !chatflowid) {
      throw new InternalFlowiseError(StatusCodes.BAD_REQUEST, 'Error: faqId and chatflowId are required')
    }
    const dbResponse = await utilBuildFaq(faqId, chatflowid, chatId, chatflow, user)
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: predictionsServices.buildFaq - ${getErrorMessage(error)}`)
  }
}

export default {
  buildChatflow,
  buildFaq
}
