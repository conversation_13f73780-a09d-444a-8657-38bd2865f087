import { ConfidentialClientApplication, AuthenticationResult } from '@azure/msal-node'
import axios from 'axios'
import { StatusCodes } from 'http-status-codes'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import { User, UserRole } from '../../database/entities/User'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { v4 as uuidv4 } from 'uuid'

interface MicrosoftUserInfo {
  id: string
  displayName: string
  mail: string
  userPrincipalName: string
  givenName?: string
  surname?: string
}

interface MicrosoftOAuthConfig {
  clientId: string
  clientSecret: string
  tenantId: string
}

class MicrosoftOAuthService {
  private msalInstance: ConfidentialClientApplication | null = null
  private config: MicrosoftOAuthConfig

  constructor() {
    this.config = {
      clientId: process.env.MICROSOFT_CLIENT_ID || '',
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET || '',
      tenantId: process.env.MICROSOFT_TENANT_ID || 'common'
    }

    if (!this.config.clientId || !this.config.clientSecret) {
      console.warn('🔐 [Microsoft OAuth]: Missing Microsoft OAuth configuration')
      return
    }

    this.initializeMSAL()
  }

  private async ensureMicrosoftOAuthColumns(dataSource: any) {
    try {
      const queryRunner = dataSource.createQueryRunner()
      await queryRunner.connect()

      // Check if microsoftId column exists
      const microsoftIdExists = await queryRunner.hasColumn('users', 'microsoftId')
      if (!microsoftIdExists) {
        console.log('🔐 [Microsoft OAuth]: Adding microsoftId column to users table')
        await queryRunner.query(`ALTER TABLE "users" ADD COLUMN "microsoftId" VARCHAR(255) NULL`)
      }

      // Check if displayName column exists
      const displayNameExists = await queryRunner.hasColumn('users', 'displayName')
      if (!displayNameExists) {
        console.log('🔐 [Microsoft OAuth]: Adding displayName column to users table')
        await queryRunner.query(`ALTER TABLE "users" ADD COLUMN "displayName" VARCHAR(255) NULL`)
      }

      // Create index if it doesn't exist
      try {
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_USER_MICROSOFT_ID" ON "users" ("microsoftId")`)
      } catch (error) {
        // Index might already exist, ignore error
        console.log('🔐 [Microsoft OAuth]: Index creation skipped (might already exist)')
      }

      await queryRunner.release()
      console.log('🔐 [Microsoft OAuth]: Microsoft OAuth columns ensured')
    } catch (error) {
      console.error('🔐 [Microsoft OAuth]: Error ensuring Microsoft OAuth columns', error)
      // Don't throw error, continue with service
    }
  }

  private initializeMSAL() {
    try {
      const msalConfig = {
        auth: {
          clientId: this.config.clientId,
          clientSecret: this.config.clientSecret,
          authority: `https://login.microsoftonline.com/${this.config.tenantId}`
        }
      }

      this.msalInstance = new ConfidentialClientApplication(msalConfig)
      console.log('🔐 [Microsoft OAuth]: MSAL instance initialized successfully')
    } catch (error) {
      console.error('🔐 [Microsoft OAuth]: Failed to initialize MSAL instance', error)
      throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Failed to initialize Microsoft OAuth')
    }
  }

  async validateAccessToken(accessToken: string): Promise<MicrosoftUserInfo> {
    try {
      console.log('🔐 [Microsoft OAuth]: Validating access token with Microsoft Graph API')
      
      const response = await axios.get('https://graph.microsoft.com/v1.0/me', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      })

      const userInfo: MicrosoftUserInfo = response.data
      console.log('🔐 [Microsoft OAuth]: Successfully validated access token', {
        userId: userInfo.id,
        displayName: userInfo.displayName,
        email: userInfo.mail || userInfo.userPrincipalName
      })

      return userInfo
    } catch (error: any) {
      console.error('🔐 [Microsoft OAuth]: Failed to validate access token', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText
      })

      if (error.response?.status === 401) {
        throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Invalid or expired Microsoft access token')
      }

      throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Failed to validate Microsoft access token')
    }
  }

  async findOrCreateUser(microsoftUserInfo: MicrosoftUserInfo): Promise<User> {
    try {
      const appServer = getRunningExpressApp()
      const userRepository = appServer.AppDataSource.getRepository(User)

      // Get email from Microsoft user info
      const email = microsoftUserInfo.mail || microsoftUserInfo.userPrincipalName

      if (!email) {
        throw new InternalFlowiseError(StatusCodes.BAD_REQUEST, 'Không thể lấy email từ tài khoản Microsoft')
      }

      // Try to find user by email
      let user = await userRepository.findOne({
        where: { email: email }
      })

      if (user) {
        console.log('🔐 [Microsoft OAuth]: Found existing user by email', {
          userId: user.id,
          username: user.username,
          email: email
        })

        // User exists, just return it (no need to update anything)
        return user
      }

      // Create new user
      console.log('🔐 [Microsoft OAuth]: Creating new user from Microsoft account', {
        email: email,
        displayName: microsoftUserInfo.displayName
      })

      const newUser = new User()
      newUser.id = uuidv4()
      newUser.username = email
      newUser.email = email
      newUser.password = await bcrypt.hash(uuidv4(), 10) // Random password for OAuth users
      newUser.role = UserRole.USER
      newUser.active = true
      newUser.groupname = 'Microsoft_Users' // Default group for Microsoft OAuth users

      const savedUser = await userRepository.save(newUser)
      console.log('🔐 [Microsoft OAuth]: Successfully created new user', {
        userId: savedUser.id,
        username: savedUser.username,
        email: savedUser.email
      })

      return savedUser
    } catch (error: any) {
      console.error('🔐 [Microsoft OAuth]: Failed to find or create user', {
        error: error.message,
        microsoftId: microsoftUserInfo.id
      })
      throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Failed to process Microsoft user')
    }
  }

  async loginWithMicrosoft(accessToken: string): Promise<{ user: User; accessToken: string; refreshToken: string }> {
    try {
      console.log('🔐 [Microsoft OAuth]: Starting Microsoft OAuth login process')

      // Validate the access token with Microsoft Graph API
      const microsoftUserInfo = await this.validateAccessToken(accessToken)

      // Find or create user in our database
      const user = await this.findOrCreateUser(microsoftUserInfo)

      // Check if user is active
      if (!user.active) {
        throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Tài khoản đã bị vô hiệu hoá')
      }

      // Generate JWT tokens
      if (!process.env.ACCESS_TOKEN_SECRET || !process.env.REFRESH_TOKEN_SECRET) {
        throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Bí mật token không được xác định')
      }

      const jwtAccessToken = jwt.sign({ id: user.id, username: user.username }, process.env.ACCESS_TOKEN_SECRET)
      const jwtRefreshToken = jwt.sign({ id: user.id, username: user.username }, process.env.REFRESH_TOKEN_SECRET)

      console.log('🔐 [Microsoft OAuth]: Successfully completed Microsoft OAuth login', {
        userId: user.id,
        username: user.username
      })

      return {
        user,
        accessToken: jwtAccessToken,
        refreshToken: jwtRefreshToken
      }
    } catch (error: any) {
      console.error('🔐 [Microsoft OAuth]: Microsoft OAuth login failed', {
        error: error.message
      })

      if (error instanceof InternalFlowiseError) {
        throw error
      }

      throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Microsoft OAuth login failed')
    }
  }
}

export default new MicrosoftOAuthService()
