import axios from 'axios'
import { StatusCodes } from 'http-status-codes'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import { User, UserRole } from '../../database/entities/User'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { v4 as uuidv4 } from 'uuid'

interface MicrosoftUserInfo {
  id: string
  displayName: string
  mail: string
  userPrincipalName: string
  givenName?: string
  surname?: string
}

interface MicrosoftOAuthConfig {
  clientId: string
  clientSecret: string
  tenantId: string
}

class MicrosoftOAuthService {
  private config: MicrosoftOAuthConfig

  constructor() {
    this.config = {
      clientId: process.env.MICROSOFT_CLIENT_ID || '',
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET || '',
      tenantId: process.env.MICROSOFT_TENANT_ID || 'common'
    }

    if (!this.config.clientId || !this.config.clientSecret) {
      console.warn('🔐 [Microsoft OAuth]: Microsoft OAuth configuration is incomplete. Please set MICROSOFT_CLIENT_ID and MICROSOFT_CLIENT_SECRET environment variables.')
    }
  }

  /**
   * Validate Microsoft access token with Microsoft Graph API
   */
  async validateAccessToken(accessToken: string): Promise<MicrosoftUserInfo> {
    try {
      console.log('🔐 [Microsoft OAuth]: Validating access token with Microsoft Graph API')

      const response = await axios.get('https://graph.microsoft.com/v1.0/me', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      })

      console.log('🔐 [Microsoft OAuth]: Successfully validated access token', {
        userId: response.data.id,
        email: response.data.mail || response.data.userPrincipalName,
        displayName: response.data.displayName
      })

      return response.data
    } catch (error: any) {
      console.error('🔐 [Microsoft OAuth]: Failed to validate access token', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText
      })

      if (error.response?.status === 401) {
        throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Invalid or expired Microsoft access token')
      } else if (error.response?.status === 403) {
        throw new InternalFlowiseError(StatusCodes.FORBIDDEN, 'Insufficient permissions to access Microsoft Graph API')
      } else {
        throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Failed to validate Microsoft access token')
      }
    }
  }

  /**
   * Find or create user based on Microsoft user info
   * Uses existing User entity fields without adding new ones
   */
  async findOrCreateUser(microsoftUserInfo: MicrosoftUserInfo): Promise<User> {
    const appServer = getRunningExpressApp()
    const userRepository = appServer.AppDataSource.getRepository(User)

    // Use email from Microsoft (mail or userPrincipalName)
    const email = microsoftUserInfo.mail || microsoftUserInfo.userPrincipalName
    if (!email) {
      throw new InternalFlowiseError(StatusCodes.BAD_REQUEST, 'Microsoft account does not have a valid email address')
    }

    console.log('🔐 [Microsoft OAuth]: Looking for existing user', { email })

    // Try to find existing user by email
    let user = await userRepository.findOne({
      where: { email: email.toLowerCase() }
    })

    if (user) {
      console.log('🔐 [Microsoft OAuth]: Found existing user', {
        userId: user.id,
        username: user.username,
        email: user.email
      })

      // Update user's display name using displayPrefixes field (existing field)
      if (microsoftUserInfo.displayName && user.displayPrefixes !== microsoftUserInfo.displayName) {
        user.displayPrefixes = microsoftUserInfo.displayName
        await userRepository.save(user)
        console.log('🔐 [Microsoft OAuth]: Updated user display name', {
          userId: user.id,
          displayName: microsoftUserInfo.displayName
        })
      }

      return user
    }

    // Create new user using existing fields only
    console.log('🔐 [Microsoft OAuth]: Creating new user from Microsoft account', {
      email: email,
      displayName: microsoftUserInfo.displayName
    })

    const newUser = new User()
    newUser.id = uuidv4()
    newUser.username = email // Use email as username
    newUser.email = email.toLowerCase()
    newUser.password = await bcrypt.hash(uuidv4(), 10) // Random password for OAuth users
    newUser.role = UserRole.USER
    newUser.active = true
    newUser.groupname = 'Microsoft_Users' // Default group for Microsoft OAuth users
    newUser.displayPrefixes = microsoftUserInfo.displayName || '' // Store display name in existing field

    const savedUser = await userRepository.save(newUser)
    console.log('🔐 [Microsoft OAuth]: Successfully created new user', {
      userId: savedUser.id,
      username: savedUser.username,
      email: savedUser.email,
      displayName: savedUser.displayPrefixes
    })

    return savedUser
  }

  /**
   * Main login method for Microsoft OAuth
   */
  async loginWithMicrosoft(accessToken: string): Promise<{ user: User; accessToken: string; refreshToken: string }> {
    try {
      console.log('🔐 [Microsoft OAuth]: Starting Microsoft OAuth login process')

      // Validate the access token with Microsoft Graph API
      const microsoftUserInfo = await this.validateAccessToken(accessToken)

      // Find or create user in our database
      const user = await this.findOrCreateUser(microsoftUserInfo)

      // Check if user is active
      if (!user.active) {
        throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Account has been deactivated')
      }

      // Generate JWT tokens
      if (!process.env.ACCESS_TOKEN_SECRET || !process.env.REFRESH_TOKEN_SECRET) {
        throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Token secrets are not configured')
      }

      const jwtAccessToken = jwt.sign(
        { 
          id: user.id, 
          username: user.username,
          email: user.email,
          role: user.role,
          loginMethod: 'microsoft'
        },
        process.env.ACCESS_TOKEN_SECRET,
        { expiresIn: '1d' }
      )

      const refreshToken = jwt.sign(
        { 
          id: user.id, 
          username: user.username,
          email: user.email,
          loginMethod: 'microsoft'
        },
        process.env.REFRESH_TOKEN_SECRET,
        { expiresIn: '7d' }
      )

      console.log('🔐 [Microsoft OAuth]: Microsoft OAuth login completed successfully', {
        userId: user.id,
        username: user.username,
        email: user.email,
        displayName: user.displayPrefixes
      })

      return {
        user,
        accessToken: jwtAccessToken,
        refreshToken
      }
    } catch (error: any) {
      console.error('🔐 [Microsoft OAuth]: Microsoft OAuth login failed', {
        error: error.message,
        statusCode: error.statusCode || StatusCodes.INTERNAL_SERVER_ERROR
      })
      throw error
    }
  }
}

export default new MicrosoftOAuthService()
