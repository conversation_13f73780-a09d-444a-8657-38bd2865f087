// Microsoft OAuth2 login
async loginWithMicrosoft(accessToken) {
    try {
        logger.info('🔐 [Microsoft OAuth] Starting Microsoft login process', {
            hasAccessToken: !!accessToken,
            tokenLength: accessToken?.length
        })

        if (!accessToken) {
            logger.error('🔐 [Microsoft OAuth] No access token provided')
            throw new Error('Access token is required')
        }

        // Get Microsoft Graph API configuration
        const clientId = process.env.MICROSOFT_CLIENT_ID
        const clientSecret = process.env.MICROSOFT_CLIENT_SECRET
        const tenantId = process.env.MICROSOFT_TENANT_ID

        if (!clientId || !clientSecret) {
            logger.error('🔐 [Microsoft OAuth] Missing Microsoft configuration', {
                hasClientId: !!clientId,
                hasClientSecret: !!clientSecret
            })
            throw new Error('Microsoft OAuth configuration is incomplete')
        }

        logger.info('🔐 [Microsoft OAuth] Microsoft configuration loaded', {
            hasClientId: !!clientId,
            hasClientSecret: !!clientSecret,
            hasTenantId: !!tenantId
        })

        // Call Microsoft Graph API to get user info
        const graphApiUrl = 'https://graph.microsoft.com/v1.0/me'
        
        logger.info('🔐 [Microsoft OAuth] Calling Microsoft Graph API', {
            url: graphApiUrl,
            hasAccessToken: !!accessToken
        })

        const graphResponse = await axios.get(graphApiUrl, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        })

        logger.success('🔐 [Microsoft OAuth] Microsoft Graph API response received', {
            status: graphResponse.status,
            hasData: !!graphResponse.data,
            userId: graphResponse.data?.id,
            userPrincipalName: graphResponse.data?.userPrincipalName
        })

        const userData = graphResponse.data

        if (!userData || !userData.id) {
            logger.error('🔐 [Microsoft OAuth] Invalid user data from Microsoft Graph API', {
                hasUserData: !!userData,
                userId: userData?.id,
                userPrincipalName: userData?.userPrincipalName
            })
            throw new Error('Invalid user data received from Microsoft')
        }

        // Extract user information
        const microsoftId = userData.id
        const email = userData.mail || userData.userPrincipalName
        const displayName = userData.displayName || userData.userPrincipalName
        const firstName = userData.givenName || ''
        const lastName = userData.surname || ''

        logger.info('🔐 [Microsoft OAuth] User data extracted', {
            microsoftId,
            email,
            displayName,
            firstName,
            lastName
        })

        if (!email) {
            logger.error('🔐 [Microsoft OAuth] No email found in Microsoft user data', {
                microsoftId,
                displayName,
                availableFields: Object.keys(userData)
            })
            throw new Error('Không thể lấy email từ tài khoản Microsoft')
        }

        // Check if user exists
        let user = await this.User.findOne({
            where: {
                [Op.or]: [
                    { microsoftId: microsoftId },
                    { email: email }
                ]
            }
        })

        logger.info('🔐 [Microsoft OAuth] User lookup result', {
            userExists: !!user,
            microsoftId,
            email
        })

        if (user) {
            // Update existing user with Microsoft info
            logger.info('🔐 [Microsoft OAuth] Updating existing user with Microsoft data', {
                userId: user.id,
                microsoftId,
                displayName
            })

            await user.update({
                microsoftId: microsoftId,
                displayName: displayName,
                firstName: firstName,
                lastName: lastName,
                lastLoginAt: new Date()
            })

            logger.success('🔐 [Microsoft OAuth] Existing user updated successfully', {
                userId: user.id,
                microsoftId,
                displayName
            })
        } else {
            // Create new user
            logger.info('🔐 [Microsoft OAuth] Creating new user from Microsoft data', {
                microsoftId,
                email,
                displayName
            })

            user = await this.User.create({
                email: email,
                microsoftId: microsoftId,
                displayName: displayName,
                firstName: firstName,
                lastName: lastName,
                password: null, // No password for OAuth users
                isActive: true,
                lastLoginAt: new Date()
            })

            logger.success('🔐 [Microsoft OAuth] New user created successfully', {
                userId: user.id,
                microsoftId,
                email,
                displayName
            })
        }

        // Generate JWT tokens
        logger.info('🔐 [Microsoft OAuth] Generating JWT tokens', {
            userId: user.id,
            email: user.email
        })

        const accessToken = jwt.sign(
            { 
                id: user.id, 
                email: user.email,
                loginMethod: 'microsoft'
            },
            process.env.JWT_SECRET,
            { expiresIn: '1d' }
        )

        const refreshToken = jwt.sign(
            { 
                id: user.id, 
                email: user.email,
                loginMethod: 'microsoft'
            },
            process.env.JWT_REFRESH_SECRET,
            { expiresIn: '7d' }
        )

        logger.success('🔐 [Microsoft OAuth] JWT tokens generated successfully', {
            userId: user.id,
            hasAccessToken: !!accessToken,
            hasRefreshToken: !!refreshToken
        })

        // Return user data and tokens
        const result = {
            user: {
                id: user.id,
                email: user.email,
                microsoftId: user.microsoftId,
                displayName: user.displayName,
                firstName: user.firstName,
                lastName: user.lastName,
                isActive: user.isActive,
                lastLoginAt: user.lastLoginAt
            },
            accessToken,
            refreshToken
        }

        logger.success('🔐 [Microsoft OAuth] Microsoft login completed successfully', {
            userId: user.id,
            email: user.email,
            microsoftId: user.microsoftId
        })

        return result

    } catch (error) {
        logger.error('🔐 [Microsoft OAuth] Error during Microsoft login', {
            error: error.message,
            stack: error.stack,
            response: error.response?.data,
            status: error.response?.status
        })

        if (error.response?.status === 401) {
            throw new Error('Token không hợp lệ hoặc đã hết hạn')
        } else if (error.response?.status === 403) {
            throw new Error('Không có quyền truy cập vào thông tin người dùng')
        } else if (error.message.includes('Network Error')) {
            throw new Error('Không thể kết nối đến Microsoft. Vui lòng kiểm tra kết nối mạng.')
        } else {
            throw new Error(`Đăng nhập Microsoft thất bại: ${error.message}`)
        }
    }
} 