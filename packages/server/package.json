{"name": "flowise", "version": "2.2.1", "description": "Flowiseai Server", "main": "dist/index", "types": "dist/index.d.ts", "bin": {"flowise": "./bin/run"}, "files": ["bin", "marketplaces", "dist", "npm-shrinkwrap.json", "oclif.manifest.json", "oauth2.html"], "oclif": {"bin": "flowise", "commands": "./dist/commands"}, "scripts": {"build": "tsc", "start": "run-script-os", "clean": "<PERSON><PERSON><PERSON> dist", "nuke": "rimraf dist node_modules .turbo", "start:windows": "cd bin && run start", "start:default": "cd bin && ./run start", "dev": "tsc-watch --noClear -p ./tsconfig.json --onSuccess \"pnpm start\"", "oclif-dev": "run-script-os", "oclif-dev:windows": "cd bin && dev start", "oclif-dev:default": "cd bin && ./dev start", "postpack": "shx rm -f oclif.manifest.json", "prepack": "pnpm build && oclif manifest && oclif readme", "typeorm": "typeorm-ts-node-commonjs", "typeorm:migration-generate": "pnpm typeorm migration:generate -d ./src/utils/typeormDataSource.ts", "typeorm:migration-run": "pnpm typeorm migration:run -d ./src/utils/typeormDataSource.ts", "watch": "tsc --watch", "version": "oclif readme && git add README.md", "cypress:open": "cypress open", "cypress:run": "cypress run", "e2e": "start-server-and-test dev http://localhost:3000 cypress:run", "cypress:ci": "START_SERVER_AND_TEST_INSECURE=1 start-server-and-test start https-get://localhost:3000 cypress:run", "test": "jest"}, "keywords": [], "homepage": "https://flowiseai.com", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">=18.15.0 <19.0.0 || ^20"}, "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@aws-sdk/client-bedrock-runtime": "3.422.0", "@aws-sdk/client-s3": "^3.427.0", "@aws-sdk/client-secrets-manager": "^3.699.0", "@aws-sdk/s3-request-presigner": "^3.427.0", "@modelcontextprotocol/sdk": "^1.10.1", "@oclif/core": "^1.13.10", "@opentelemetry/api": "^1.3.0", "@opentelemetry/auto-instrumentations-node": "^0.52.0", "@opentelemetry/core": "1.27.0", "@opentelemetry/exporter-metrics-otlp-grpc": "0.54.0", "@opentelemetry/exporter-metrics-otlp-http": "0.54.0", "@opentelemetry/exporter-metrics-otlp-proto": "0.54.0", "@opentelemetry/exporter-trace-otlp-grpc": "0.54.0", "@opentelemetry/exporter-trace-otlp-http": "0.54.0", "@opentelemetry/exporter-trace-otlp-proto": "0.54.0", "@opentelemetry/resources": "1.27.0", "@opentelemetry/sdk-metrics": "1.27.0", "@opentelemetry/sdk-node": "^0.54.0", "@opentelemetry/sdk-trace-base": "1.27.0", "@opentelemetry/semantic-conventions": "1.27.0", "@sentry/node": "^8.54.0", "@sentry/profiling-node": "^8.54.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "async-mutex": "^0.4.0", "axios": "1.6.2", "bcryptjs": "^2.4.3", "content-disposition": "0.5.4", "cors": "^2.8.5", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "dotenv": "^16.0.0", "express": "^4.17.3", "express-basic-auth": "^1.2.1", "express-rate-limit": "^6.9.0", "flowise-components": "workspace:^", "flowise-ui": "workspace:^", "global-agent": "^3.0.0", "http-errors": "^2.0.0", "http-proxy-middleware": "2.0.9", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "langchainhub": "^0.0.11", "lodash": "^4.17.21", "moment": "^2.29.3", "moment-timezone": "^0.5.34", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.3", "openai": "^4.57.3", "pg": "^8.11.1", "posthog-node": "^3.5.0", "prom-client": "^15.1.3", "reflect-metadata": "^0.1.13", "sanitize-html": "^2.11.0", "socket.io": "^4.6.1", "sqlite3": "^5.1.6", "typeorm": "^0.3.6", "uuid": "^9.0.1", "winston": "^3.9.0", "xml-js": "^1.6.11", "zod": "3.22.4"}, "devDependencies": {"@types/content-disposition": "0.5.8", "@types/cors": "^2.8.12", "@types/crypto-js": "^4.1.1", "@types/multer": "^1.4.7", "@types/sanitize-html": "^2.9.5", "@types/xml-js": "^1.0.0", "concurrently": "^7.1.0", "cypress": "^13.13.0", "nodemon": "^2.0.22", "oclif": "^3", "rimraf": "^5.0.5", "run-script-os": "^1.1.6", "shx": "^0.3.3", "start-server-and-test": "^2.0.3", "ts-node": "^10.7.0", "tsc-watch": "^6.0.4", "typescript": "^5.4.5"}}