<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Agent Studio: Build & Automate Your AI Workforce</title>
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <!-- Meta Tags-->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2296f3" />
    <meta name="title" content="Agent Studio: Build & Automate Your AI Workforce" />
    <meta name="description" content="Agent Studio helps you to better integrate Web3 with existing Web2 applications" />
    <meta name="keywords" content="react, material-ui, reactjs, reactjs, workflow automation, web3, web2, blockchain" />
    <meta name="author" content="CodedThemes" />
    <!-- Open Graph / Facebook -->
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="website" />
    <!--        <meta property="og:url" content="https://Agent Studio.com/" />-->
    <!--        <meta property="og:site_name" content="Agent Studio.com" />-->
    <meta property="article:publisher" content="https://www.facebook.com/codedthemes" />
    <meta property="og:title" content="Agent Studio: Build & Automate Your AI Workforce" />
    <meta
      property="og:description"
      content="Build customized LLM orchestration flow & agents, enable quick iterations from testing to production"
    />
    <!--        <meta property="og:image" content="https://Agent Studio.com/og-image/og-facebook.png" />-->
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <!--        <meta property="twitter:url" content="https://Agent Studio.com" />-->
    <meta property="twitter:title" content="Agent Studio: Build & Automate Your AI Workforce" />
    <meta
      property="twitter:description"
      content="Build customized LLM orchestration flow & agents, enable quick iterations from testing to production"
    />
    <!--        <meta property="twitter:image" content="https://Agent Studio.com/og-image/og-twitter.png" />-->
    <meta name="twitter:creator" content="@codedthemes" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->

    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <div id="portal"></div>
    <!--
          This HTML file is a template.
          If you open it directly in the browser, you will see an empty page.

          You can add webfonts, meta tags, or analytics to this file.
          The build step will place the bundled scripts into the <body> tag.

          To begin the development, run `pnpm start`.
          To create a production bundle, use `pnpm build`.
   -->
  </body>
</html>
