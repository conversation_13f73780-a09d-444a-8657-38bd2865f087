<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Agent Studio: Build & Automate Your AI Workforce</title>
    <link rel="icon" href="favicon.ico" />
    <!-- Meta Tags-->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2296f3" />
    <meta name="title" content="Agent Studio: Build & Automate Your AI Workforce" />
    <meta name="description" content="Drag & drop UI to build your customized LLM flow" />
    <meta name="keywords" content="react, material-ui, workflow automation, llm, artificial-intelligence" />
    <meta name="author" content="Agent Studio" />
    <!-- Open Graph / Facebook -->
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="website" />
    <!--        <meta property="og:url" content="https://Agent Studio.com/" />-->
    <!--        <meta property="og:site_name" content="Agent Studio.com" />-->
    <meta property="og:title" content="Agent Studio: Build & Automate Your AI Workforce" />
    <meta property="og:description" content="Drag & drop UI to build your customized LLM flow" />
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <!--        <meta property="twitter:url" content="https://twitter.com/Agent Studio" />-->
    <meta property="twitter:title" content="Agent Studio: Build & Automate Your AI Workforce" />
    <meta property="twitter:description" content="Drag & drop UI to build your customized LLM flow" />
    <!--        <meta name="twitter:creator" content="@Agent Studio" />-->

    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <div id="portal"></div>
    <script type="module" src="src/index.jsx"></script>
    <script>
      if (global === undefined) {
        var global = window
      }
    </script>
  </body>
</html>
