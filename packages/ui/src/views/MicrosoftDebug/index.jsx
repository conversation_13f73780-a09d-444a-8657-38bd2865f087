import React, { useState, useEffect } from 'react'
import { Box, Typography, Paper, List, ListItem, ListItemText, Button, Alert } from '@mui/material'
import { useNavigate } from 'react-router-dom'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'

const MicrosoftDebug = () => {
  const [logs, setLogs] = useState([])
  const navigate = useNavigate()

  useEffect(() => {
    // Load all logs
    const allLogs = microsoftOAuthDebugger.getLogs()
    setLogs(allLogs)
    
    // Enable debug mode
    localStorage.setItem('microsoft-oauth-debug', 'true')
  }, [])

  const refreshLogs = () => {
    const allLogs = microsoftOAuthDebugger.getLogs()
    setLogs(allLogs)
  }

  const clearLogs = () => {
    microsoftOAuthDebugger.clearLogs()
    localStorage.removeItem('microsoft-oauth-logs')
    setLogs([])
  }

  const exportLogs = () => {
    microsoftOAuthDebugger.exportLogs()
  }

  const getLogColor = (level) => {
    switch (level) {
      case 'error': return '#f44336'
      case 'warning': return '#ff9800'
      case 'success': return '#4caf50'
      case 'info': return '#2196f3'
      default: return '#757575'
    }
  }

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Microsoft OAuth Debug Logs
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        This page shows all Microsoft OAuth debug logs. Access this page at: <strong>/microsoft-debug</strong>
      </Alert>

      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <Button variant="contained" onClick={refreshLogs}>
          Refresh Logs
        </Button>
        <Button variant="outlined" onClick={clearLogs} color="warning">
          Clear All Logs
        </Button>
        <Button variant="outlined" onClick={exportLogs}>
          Export Logs
        </Button>
        <Button variant="outlined" onClick={() => navigate('/login')}>
          Back to Login
        </Button>
      </Box>

      <Typography variant="h6" sx={{ mb: 2 }}>
        Total Logs: {logs.length}
      </Typography>

      {logs.length === 0 ? (
        <Alert severity="warning">
          No logs found. Try using Microsoft OAuth login to generate logs.
        </Alert>
      ) : (
        <Paper sx={{ maxHeight: '70vh', overflow: 'auto' }}>
          <List>
            {logs.map((log, index) => (
              <ListItem 
                key={index} 
                sx={{ 
                  borderBottom: '1px solid #e0e0e0',
                  '&:hover': { backgroundColor: '#f5f5f5' }
                }}
              >
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          fontWeight: 600,
                          color: getLogColor(log.level),
                          minWidth: 80
                        }}
                      >
                        [{log.level.toUpperCase()}]
                      </Typography>
                      <Typography variant="body2">
                        {log.message}
                      </Typography>
                    </Box>
                  }
                  secondary={
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        <strong>Time:</strong> {new Date(log.timestamp).toLocaleString()}
                      </Typography>
                      <br />
                      <Typography variant="caption" color="text.secondary">
                        <strong>URL:</strong> {log.url}
                      </Typography>
                      {log.data && (
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="caption" color="text.secondary">
                            <strong>Data:</strong>
                          </Typography>
                          <Box 
                            component="pre" 
                            sx={{ 
                              fontSize: '0.7rem', 
                              mt: 0.5, 
                              whiteSpace: 'pre-wrap',
                              backgroundColor: '#f5f5f5',
                              p: 1,
                              borderRadius: 1,
                              overflow: 'auto',
                              maxHeight: 200
                            }}
                          >
                            {JSON.stringify(log.data, null, 2)}
                          </Box>
                        </Box>
                      )}
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      )}

      <Box sx={{ mt: 3 }}>
        <Typography variant="body2" color="text.secondary">
          <strong>Debug Instructions:</strong>
          <br />
          1. Try Microsoft OAuth login to generate logs
          <br />
          2. Check this page for detailed logs
          <br />
          3. Use browser console for real-time logs
          <br />
          4. Enable debug mode: localStorage.setItem('microsoft-oauth-debug', 'true')
        </Typography>
      </Box>
    </Box>
  )
}

export default MicrosoftDebug
