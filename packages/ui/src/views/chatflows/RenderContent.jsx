import React, { useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import { Box, FormControl, FormControlLabel, Radio, RadioGroup, Skeleton, Stack } from '@mui/material'
import { FlowListTable } from '@/ui-component/table/FlowListTable'
import ItemCard from '@/ui-component/cards/ItemCard'
import WorkflowEmptySVG from '@/assets/images/workflow_empty.svg'
import { gridSpacing } from '@/store/constant'
import PaginationComponent from '@/ui-component/pagination/PaginationComponent'

const RenderContent = ({
  data: dataInput,
  isLoading,
  filterFunction,
  updateFlowsApi,
  isAdmin = true,
  view,
  goToCanvas,
  images,
  setError,
  isUser,
  msgEmpty,
  isAgentCanvas,
  pagination,
  currentPage,
  setCurrentPage,
  onFilterChange
}) => {
  const [data, setData] = useState(null)

  const handleFilterChange = (e) => {
    const newFilter = e.target.value
    onFilterChange?.(newFilter)
    setData(null)
  }

  const handleOnchangePage = async (_, page) => {
    await setCurrentPage(page)
    await updateFlowsApi.request({ currentPageInput: page })
  }

  useEffect(() => {
    // if (dataInput && filter === 'public') {
    //   setData(dataInput.filter((item) => item.isPublic))
    // }
    // if (dataInput && filter === 'unpublic') {
    //   setData(dataInput.filter((item) => !item.isPublic))
    // }
    // if (dataInput && filter === 'all') {
    //   setData(dataInput)
    // }
    setData(dataInput)
  }, [dataInput])

  return (
    <div className='relative'>
      {pagination && pagination.totalPages > 1 && pagination.totalCount > 0 && (
        <div className='absolute top-[-41px] right-0'>
          <PaginationComponent count={pagination.totalPages} page={currentPage} onChange={handleOnchangePage} />
        </div>
      )}
      {isAdmin && (
        <FormControl className='absolute top-[-45px] left-0'>
          <RadioGroup
            onChange={handleFilterChange}
            aria-labelledby='demo-radio-buttons-group-label'
            defaultValue='all'
            name='radio-buttons-group'
            row
          >
            <FormControlLabel value='all' control={<Radio />} label='Tất cả' />
            <FormControlLabel value='public' control={<Radio />} label='Công bố' />
            <FormControlLabel value='unpublic' control={<Radio />} label='Chưa công bố' />
          </RadioGroup>
        </FormControl>
      )}
      {view === 'card' ? (
        isLoading ? (
          <Box display='grid' gridTemplateColumns='repeat(3, 1fr)' gap={gridSpacing}>
            <Skeleton variant='rounded' height={160} />
            <Skeleton variant='rounded' height={160} />
            <Skeleton variant='rounded' height={160} />
          </Box>
        ) : (
          data?.length > 0 && (
            <Box display='grid' gridTemplateColumns='repeat(3, 1fr)' gap={gridSpacing}>
              {data?.filter(filterFunction).map((item, index) => (
                <ItemCard key={index} onClick={() => goToCanvas(item)} data={item} images={images[item.id]} />
              ))}
            </Box>
          )
        )
      ) : (
        // data?.length > 0 && (
        <FlowListTable
          data={data}
          images={images}
          isLoading={isLoading}
          filterFunction={filterFunction}
          updateFlowsApi={updateFlowsApi}
          setError={setError}
          isAgentCanvas={isAgentCanvas}
        />
        // )
      )}
      {data?.length === 0 && !isLoading && (
        <Stack sx={{ alignItems: 'center', justifyContent: 'center' }} flexDirection='column'>
          <Box sx={{ p: 2, height: 'auto' }}>
            <img style={{ objectFit: 'cover', height: '25vh', width: 'auto' }} src={WorkflowEmptySVG} alt='WorkflowEmptySVG' />
          </Box>
          <div>{msgEmpty || 'Người dùng chưa tạo chatflow nào, tạo mới chatflow'}</div>
        </Stack>
      )}
    </div>
  )
}

RenderContent.propTypes = {
  data: PropTypes.array,
  isLoading: PropTypes.bool.isRequired,
  filterFunction: PropTypes.func.isRequired,
  updateFlowsApi: PropTypes.object.isRequired,
  isAdmin: PropTypes.bool,
  isUser: PropTypes.bool,
  view: PropTypes.string.isRequired,
  goToCanvas: PropTypes.func.isRequired,
  images: PropTypes.object.isRequired,
  setError: PropTypes.func.isRequired,
  msgEmpty: PropTypes.string,
  isAgentCanvas: PropTypes.bool,
  pagination: PropTypes.shape({
    page: PropTypes.number.isRequired,
    pageSize: PropTypes.number.isRequired,
    totalCount: PropTypes.number.isRequired,
    totalPages: PropTypes.number.isRequired
  }).isRequired,
  currentPage: PropTypes.number.isRequired,
  setCurrentPage: PropTypes.func.isRequired,
  onFilterChange: PropTypes.func
}

export default RenderContent
