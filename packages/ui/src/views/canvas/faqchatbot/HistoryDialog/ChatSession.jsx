import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material'
import { IconCheck, IconX } from '@tabler/icons-react'
import clsx from 'clsx'
import dayjs from 'dayjs'
import PropTypes from 'prop-types'
import { memo, useMemo } from 'react'

const TH = ['ID', 'Câu hỏi', 'Trả lời', 'Thời gian', 'Người hỏi']

const ChatSession = ({ sessionId, data, chatSession, setChatSession, addToFaqFromHistory }) => {
  const handleViewChat = (chatSession) => {
    setChatSession({ open: true, data: chatSession })
  }

  const VisibleData = useMemo(() => {
    return data.map((item) => ({ ...item, updatedDate: dayjs(item.updatedDate).format('HH:mm:ss DD/MM/YYYY') }))
  }, [data])

  return (
    <>
      <TableContainer>
        <Table size='small'>
          <TableHead>
            <TableRow className={clsx(chatSession?.data?.sessionId === sessionId ? 'bg-[#ddeffd]' : 'bg-slate-100')}>
              {TH.map((th) => (
                <TableCell key={th} className='whitespace-nowrap'>
                  {th}
                </TableCell>
              ))}
              <TableCell className='whitespace-nowrap'>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {VisibleData.map((message) => (
              <TableRow
                key={message.id}
                onClick={() => handleViewChat({ chatId: message.id, messages: [message] })}
                className={clsx(
                  'cursor-pointer',
                  chatSession?.data?.sessionId === sessionId || chatSession?.data?.chatId === message.id ? 'bg-[#ddeffd]' : 'bg-white'
                )}
              >
                <TableCell className='min-w-[300px]'>{message.id}</TableCell>
                <TableCell className='truncate min-w-[500px] max-w-[500px]'>{message.question}</TableCell>
                <TableCell className='truncate min-w-[500px] max-w-[500px]'>{message.answer}</TableCell>
                <TableCell className='min-w-[200px]'>{message.updatedDate}</TableCell>
                <TableCell>{message.userName}</TableCell>
                <TableCell>
                  <button
                    className='px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600'
                    onClick={(e) => {
                      e.stopPropagation()
                      addToFaqFromHistory(message)
                    }}
                  >
                    Thêm
                  </button>
                </TableCell>{' '}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  )
}

ChatSession.propTypes = {
  sessionId: PropTypes.string,
  data: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      question: PropTypes.string,
      answer: PropTypes.string,
      isFAQ: PropTypes.bool,
      questioner: PropTypes.string,
      attachments: PropTypes.arrayOf(PropTypes.object)
    })
  ),
  chatSession: PropTypes.object,
  setChatSession: PropTypes.func.isRequired,
  addToFaqFromHistory: PropTypes.func.isRequired
}

export default memo(ChatSession)
