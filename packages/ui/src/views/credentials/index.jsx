import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { enqueueSnackbar as enqueueSnackbarAction, closeSnackbar as closeSnackbarAction } from '@/store/actions'
import moment from 'moment'

// material-ui
import { styled } from '@mui/material/styles'
import { tableCellClasses } from '@mui/material/TableCell'
import {
  Button,
  Box,
  Skeleton,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  useTheme
} from '@mui/material'

// project imports
import MainCard from '@/ui-component/cards/MainCard'
import { StyledButton } from '@/ui-component/button/StyledButton'
import CredentialListDialog from './CredentialListDialog'
import ConfirmDialog from '@/ui-component/dialog/ConfirmDialog'
import AddEditCredentialDialog from './AddEditCredentialDialog'

// API
import credentialsApi from '@/api/credentials'

// Hooks
import useApi from '@/hooks/useApi'
import useConfirm from '@/hooks/useConfirm'

// utils
import useNotifier from '@/utils/useNotifier'

// Icons
import { IconTrash, IconEdit, IconX, IconPlus } from '@tabler/icons-react'
import CredentialEmptySVG from '@/assets/images/credential_empty.svg'

// const
import { baseURL } from '@/store/constant'
import { SET_COMPONENT_CREDENTIALS } from '@/store/actions'
import ViewHeader from '@/layout/MainLayout/ViewHeader'
import ErrorBoundary from '@/ErrorBoundary'

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  borderColor: theme.palette.grey[900] + 25,
  padding: '6px 16px',

  [`&.${tableCellClasses.head}`]: {
    color: theme.palette.grey[900]
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
    height: 64
  }
}))

const StyledTableRow = styled(TableRow)(() => ({
  // hide last border
  '&:last-child td, &:last-child th': {
    border: 0
  }
}))

// ==============================|| Credentials ||============================== //

const Credentials = () => {
  const user = useSelector((state) => state.user)
  const isLogin = user?.id ? true : false
  const theme = useTheme()
  const customization = useSelector((state) => state.customization)
  const dispatch = useDispatch()
  useNotifier()

  const enqueueSnackbar = (...args) => dispatch(enqueueSnackbarAction(...args))
  const closeSnackbar = (...args) => dispatch(closeSnackbarAction(...args))

  const [error, setError] = useState(null)
  const [showCredentialListDialog, setShowCredentialListDialog] = useState(false)
  const [credentialListDialogProps, setCredentialListDialogProps] = useState({})
  const [showSpecificCredentialDialog, setShowSpecificCredentialDialog] = useState(false)
  const [specificCredentialDialogProps, setSpecificCredentialDialogProps] = useState({})
  const [credentials, setCredentials] = useState([])
  const [componentsCredentials, setComponentsCredentials] = useState([])

  const { confirm } = useConfirm()

  const { loading: isLoadingCredentialsApi, ...getAllCredentialsApi } = useApi(credentialsApi.getAllCredentials)
  const getAllComponentsCredentialsApi = useApi(credentialsApi.getAllComponentsCredentials)

  const [search, setSearch] = useState('')
  const onSearchChange = (event) => {
    setSearch(event.target.value)
  }
  function filterCredentials(data) {
    return data.credentialName.toLowerCase().indexOf(search.toLowerCase()) > -1
  }

  const listCredential = () => {
    const dialogProp = {
      title: 'Thêm mới Credential',
      componentsCredentials
    }
    setCredentialListDialogProps(dialogProp)
    setShowCredentialListDialog(true)
  }

  const addNew = (credentialComponent) => {
    const dialogProp = {
      type: 'ADD',
      cancelButtonName: 'Cancel',
      confirmButtonName: 'Add',
      credentialComponent
    }
    setSpecificCredentialDialogProps(dialogProp)
    setShowSpecificCredentialDialog(true)
  }

  const edit = (credential) => {
    const dialogProp = {
      type: 'EDIT',
      cancelButtonName: 'Cancel',
      confirmButtonName: 'Save',
      data: credential
    }
    setSpecificCredentialDialogProps(dialogProp)
    setShowSpecificCredentialDialog(true)
  }

  const deleteCredential = async (credential) => {
    const confirmPayload = {
      title: `Xoá thông tin xác thực`,
      description: `Xoá ${credential.name}?`,
      confirmButtonName: 'Xoá',
      cancelButtonName: 'Đóng'
    }
    const isConfirmed = await confirm(confirmPayload)

    if (isConfirmed) {
      try {
        const deleteResp = await credentialsApi.deleteCredential(credential.id)
        if (deleteResp.data) {
          enqueueSnackbar({
            message: 'Đã xoá thông tin xác thực',
            options: {
              key: new Date().getTime() + Math.random(),
              variant: 'success',
              action: (key) => (
                <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
                  <IconX />
                </Button>
              )
            }
          })
          onConfirm()
        }
      } catch (error) {
        enqueueSnackbar({
          message: `Không thể xoá thông tin xác thực: ${
            typeof error.response.data === 'object' ? error.response.data.message : error.response.data
          }`,
          options: {
            key: new Date().getTime() + Math.random(),
            variant: 'error',
            persist: true,
            action: (key) => (
              <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
                <IconX />
              </Button>
            )
          }
        })
        onCancel()
      }
    }
  }

  const onCredentialSelected = (credentialComponent) => {
    setShowCredentialListDialog(false)
    addNew(credentialComponent)
  }

  const onConfirm = () => {
    setShowCredentialListDialog(false)
    setShowSpecificCredentialDialog(false)
    getAllCredentialsApi.request()
  }

  useEffect(() => {
    if (isLogin) {
      getAllCredentialsApi.request()
      getAllComponentsCredentialsApi.request()
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLogin])

  useEffect(() => {
    if (getAllCredentialsApi.data) {
      setCredentials(getAllCredentialsApi.data)
    }
  }, [getAllCredentialsApi.data])

  useEffect(() => {
    if (getAllCredentialsApi.error) {
      setError(getAllCredentialsApi.error)
    }
  }, [getAllCredentialsApi.error])

  useEffect(() => {
    if (getAllComponentsCredentialsApi.data) {
      setComponentsCredentials(getAllComponentsCredentialsApi.data)
      dispatch({ type: SET_COMPONENT_CREDENTIALS, componentsCredentials: getAllComponentsCredentialsApi.data })
    }
  }, [getAllComponentsCredentialsApi.data, dispatch])

  return (
    <>
      <MainCard>
        {error ? (
          <ErrorBoundary error={error} />
        ) : (
          <Stack flexDirection='column' sx={{ gap: 3 }}>
            <ViewHeader
              onSearchChange={onSearchChange}
              search={true}
              searchPlaceholder='Tìm kiếm thông tin xác thực'
              title='Thông tin xác thực'
            >
              <StyledButton
                disabled={!isLogin}
                variant='contained'
                sx={{ borderRadius: 2, height: '100%' }}
                onClick={listCredential}
                startIcon={<IconPlus />}
              >
                Thêm
              </StyledButton>
            </ViewHeader>

            {isLogin ? (
              <TableContainer sx={{ border: 1, borderColor: theme.palette.grey[900] + 25, borderRadius: 2 }} component={Paper}>
                <Table sx={{ minWidth: 650 }} aria-label='simple table'>
                  <TableHead
                    sx={{
                      backgroundColor: customization.isDarkMode ? theme.palette.common.black : theme.palette.grey[100],
                      height: 56
                    }}
                  >
                    <TableRow>
                      <StyledTableCell>Tên</StyledTableCell>
                      <StyledTableCell>Lần cập nhật cuối</StyledTableCell>
                      <StyledTableCell>Thời gian tạo</StyledTableCell>
                      <StyledTableCell> </StyledTableCell>
                      <StyledTableCell> </StyledTableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {isLoadingCredentialsApi ? (
                      <>
                        <StyledTableRow>
                          <StyledTableCell>
                            <Skeleton variant='text' />
                          </StyledTableCell>
                          <StyledTableCell>
                            <Skeleton variant='text' />
                          </StyledTableCell>
                          <StyledTableCell>
                            <Skeleton variant='text' />
                          </StyledTableCell>
                          <StyledTableCell>
                            <Skeleton variant='text' />
                          </StyledTableCell>
                          <StyledTableCell>
                            <Skeleton variant='text' />
                          </StyledTableCell>
                        </StyledTableRow>
                        <StyledTableRow>
                          <StyledTableCell>
                            <Skeleton variant='text' />
                          </StyledTableCell>
                          <StyledTableCell>
                            <Skeleton variant='text' />
                          </StyledTableCell>
                          <StyledTableCell>
                            <Skeleton variant='text' />
                          </StyledTableCell>
                          <StyledTableCell>
                            <Skeleton variant='text' />
                          </StyledTableCell>
                          <StyledTableCell>
                            <Skeleton variant='text' />
                          </StyledTableCell>
                        </StyledTableRow>
                      </>
                    ) : (
                      <>
                        {credentials.length > 0 &&
                          credentials.filter(filterCredentials).map((credential, index) => (
                            <StyledTableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                              <StyledTableCell scope='row'>
                                <Box
                                  sx={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    gap: 1
                                  }}
                                >
                                  <Box
                                    sx={{
                                      width: 35,
                                      height: 35,
                                      borderRadius: '50%',
                                      backgroundColor: customization.isDarkMode ? theme.palette.common.white : theme.palette.grey[300] + 75
                                    }}
                                  >
                                    <img
                                      style={{
                                        width: '100%',
                                        height: '100%',
                                        padding: 5,
                                        objectFit: 'contain'
                                      }}
                                      alt={credential.credentialName}
                                      src={`${baseURL}/api/v1/components-credentials-icon/${credential.credentialName}`}
                                    />
                                  </Box>
                                  {credential.name}
                                </Box>
                              </StyledTableCell>
                              <StyledTableCell>{moment(credential.updatedDate).format('MMMM Do, YYYY')}</StyledTableCell>
                              <StyledTableCell>{moment(credential.createdDate).format('MMMM Do, YYYY')}</StyledTableCell>
                              <StyledTableCell>
                                <IconButton title='Edit' color='primary' onClick={() => edit(credential)}>
                                  <IconEdit />
                                </IconButton>
                              </StyledTableCell>
                              <StyledTableCell>
                                <IconButton title='Delete' color='error' onClick={() => deleteCredential(credential)}>
                                  <IconTrash />
                                </IconButton>
                              </StyledTableCell>
                            </StyledTableRow>
                          ))}
                      </>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <div>Đăng nhập để xem danh sách Credentials</div>
            )}
            {isLogin && !isLoadingCredentialsApi && credentials.length <= 0 && (
              <Stack sx={{ alignItems: 'center', justifyContent: 'center' }} flexDirection='column'>
                <Box sx={{ p: 2, height: 'auto' }}>
                  <img style={{ objectFit: 'cover', height: '16vh', width: 'auto' }} src={CredentialEmptySVG} alt='CredentialEmptySVG' />
                </Box>
                <div>Chưa có Credentials vào đối với user này.</div>
              </Stack>
            )}
          </Stack>
        )}
      </MainCard>
      <CredentialListDialog
        show={showCredentialListDialog}
        dialogProps={credentialListDialogProps}
        onCancel={() => setShowCredentialListDialog(false)}
        onCredentialSelected={onCredentialSelected}
      ></CredentialListDialog>
      <AddEditCredentialDialog
        show={showSpecificCredentialDialog}
        dialogProps={specificCredentialDialogProps}
        onCancel={() => setShowSpecificCredentialDialog(false)}
        onConfirm={onConfirm}
        setError={setError}
      ></AddEditCredentialDialog>
      <ConfirmDialog />
    </>
  )
}

export default Credentials
