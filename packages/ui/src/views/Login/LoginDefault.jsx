import React, { useState } from 'react'
import {
  Button,
  TextField,
  Paper,
  Box,
  Grid,
  Typography,
  Container,
  InputAdornment,
  IconButton,
  useMediaQuery,
  createTheme,
  ThemeProvider,
  CircularProgress,
  Alert
} from '@mui/material'
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined'
import VisibilityOffOutlinedIcon from '@mui/icons-material/VisibilityOffOutlined'
import LoginIcon from '@mui/icons-material/Login'
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline'
import { motion } from 'framer-motion'
import LogoSection from '@/layout/MainLayout/LogoSection'
import userApi from '@/api/user'
import { useDispatch } from 'react-redux'
import config from '@/config'
import { logoutAction } from '@/store/actions'
import MicrosoftLoginButton from '@/components/MicrosoftLoginButton'
import MicrosoftAuthDebugPanel from '@/components/MicrosoftAuthDebugPanel'

const theme = createTheme({
  palette: {
    primary: {
      main: '#3f51b5',
      light: '#757de8',
      dark: '#002984',
      contrastText: '#fff'
    },
    secondary: {
      main: '#f50057',
      light: '#ff4081',
      dark: '#c51162',
      contrastText: '#fff'
    },
    background: {
      default: '#f5f5f5'
    }
  },
  typography: {
    fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 700,
      letterSpacing: '0.03em'
    },
    h6: {
      fontWeight: 500,
      letterSpacing: '0.02em'
    },
    button: {
      fontWeight: 600,
      textTransform: 'none',
      letterSpacing: '0.03em'
    }
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '0.5rem',
          padding: '0.625rem 1.5rem',
          boxShadow: '0 0.25rem 0.375rem rgba(50, 50, 93, 0.1), 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.08)',
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'translateY(-0.0625rem)',
            boxShadow: '0 0.4375rem 0.875rem rgba(50, 50, 93, 0.1), 0 0.1875rem 0.375rem rgba(0, 0, 0, 0.08)'
          }
        },
        containedPrimary: {
          background: 'linear-gradient(45deg, #3f51b5 30%, #757de8 90%)'
        }
      }
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: '0.5rem',
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#3f51b5'
            }
          }
        }
      }
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: '1rem',
          boxShadow: '0 0.625rem 1.875rem rgba(0, 0, 0, 0.1)',
          padding: '1.5rem',
          marginBottom: '1rem'
        }
      }
    }
  }
})

const LoginDefault = () => {
  const [showPassword, setShowPassword] = useState(false)
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [msgError, setMsgError] = useState('')
  const dispatch = useDispatch()

  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  const logout = (...args) => dispatch(logoutAction(...args))

  const handleLogin = async (e) => {
    e.preventDefault()
    if (!username || !password) {
      const message = 'Vui lòng nhập tài khoản và mật khẩu.'
      setMsgError(message)
      return
    }
    setIsLoading(true)

    try {
      let resData = await userApi.loginUser({ username, password })
      resData = resData.data
      if (resData) {
        localStorage.setItem('dataLogin', JSON.stringify(resData))
        // Clear specified localStorage items after successful login
        localStorage.removeItem('chatInputHistory')
        localStorage.removeItem('57e4146c-dd6b-4eed-ae49-40223b00af25_EXTERNAL')
        localStorage.removeItem('248e6488-33f3-4b91-a75f-a0e1f76f286c_EXTERNAL')

        const redirectUrl = localStorage.getItem('redirectAfterLogin')
        if (redirectUrl) {
          localStorage.removeItem('redirectAfterLogin')
          window.location.href = redirectUrl
        } else {
          window.location.href = ''
        }
      }
    } catch (error) {
      if (error?.response?.data?.message) {
        setMsgError(error?.response?.data?.message)
      } else {
        setMsgError('Đã có lỗi xảy ra. Vui lòng thử lại sau.')
      }
      localStorage.removeItem('dataLogin')
      logout({})
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <ThemeProvider theme={theme}>
      <Container
        component='main'
        maxWidth='lg'
        sx={{
          height: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          py: 2,
          overflow: 'auto'
        }}
      >
        <Grid
          container
          component={Paper}
          elevation={6}
          sx={{
            borderRadius: '1rem',
            overflow: 'hidden',
            boxShadow: '0 0.9375rem 3.125rem rgba(0, 0, 0, 0.1)',
            maxHeight: isMobile ? 'auto' : '40rem',
            maxWidth: '62.5rem',
            width: '100%'
          }}
        >
          {/* Left side - Image */}
          {!isMobile && (
            <Grid
              item
              xs={false}
              sm={5}
              md={6}
              sx={{
                backgroundImage: 'url(https://source.unsplash.com/random?wallpapers)',
                backgroundRepeat: 'no-repeat',
                backgroundColor: (t) => (t.palette.mode === 'light' ? t.palette.grey[50] : t.palette.grey[900]),
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                position: 'relative'
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(63, 81, 181, 0.7)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  p: { xs: 2, sm: 3, md: 4 },
                  textAlign: 'center'
                }}
              >
                <Typography component='h1' variant='h3' color='white' fontWeight='bold' mb={2}>
                  Chào Mừng Trở Lại
                </Typography>
                <Typography variant='h6' color='white' paragraph>
                  Truy cập bảng điều khiển với thông tin đăng nhập của bạn
                </Typography>
              </Box>
            </Grid>
          )}

          {/* Right side - Form */}
          <Grid
            item
            xs={12}
            sm={7}
            md={6}
            component={motion.div}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Box
              sx={{
                my: { xs: 2, sm: 3, md: 4 },
                mx: { xs: 2, sm: 3, md: 4 },
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                height: '100%',
                justifyContent: 'center'
              }}
            >
              <Box component='span' sx={{ display: { xs: 'none', md: 'block' } }}>
                <LogoSection />
              </Box>
              <Typography component='h1' variant='h4' sx={{ mt: 2, mb: 3 }}>
                Đăng Nhập
              </Typography>
              <Box component='form' noValidate onSubmit={handleLogin} sx={{ width: '100%', mt: 1, maxWidth: '25rem' }}>
                <TextField
                  margin='normal'
                  required
                  fullWidth
                  id='username'
                  label='Tài khoản'
                  name='username'
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  InputLabelProps={{
                    shrink: true
                  }}
                  sx={{ mb: 1 }}
                />
                <TextField
                  margin='normal'
                  required
                  fullWidth
                  name='password'
                  label='Mật Khẩu'
                  type={showPassword ? 'text' : 'password'}
                  id='password'
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  InputLabelProps={{
                    shrink: true
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position='end'>
                        <IconButton aria-label='toggle password visibility' onClick={() => setShowPassword(!showPassword)} edge='end'>
                          {showPassword ? <VisibilityOffOutlinedIcon /> : <VisibilityOutlinedIcon />}
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                  sx={{ mb: msgError ? 1 : 2 }}
                />

                {msgError && (
                  <Alert
                    severity='error'
                    icon={<ErrorOutlineIcon fontSize='inherit' />}
                    sx={{
                      mb: 2,
                      mt: 1,
                      borderRadius: '8px',
                      '& .MuiAlert-icon': {
                        color: '#f44336'
                      }
                    }}
                  >
                    {msgError}
                  </Alert>
                )}

                <Button
                  type='submit'
                  onClick={handleLogin}
                  fullWidth
                  variant='contained'
                  color='primary'
                  size='large'
                  disabled={isLoading}
                  sx={{
                    mt: 3,
                    mb: 2,
                    py: 1.5,
                    fontSize: { xs: '0.875rem', sm: '1rem' }
                  }}
                  startIcon={isLoading ? <CircularProgress size={20} className='text-white' /> : <LoginIcon />}
                >
                  <span className='text-white'>{isLoading ? 'Đang xử lý...' : 'Đăng Nhập'}</span>
                </Button>

                {/* Microsoft Login Button */}
                <MicrosoftLoginButton
                  onError={(error) => setMsgError(error)}
                  disabled={isLoading}
                  fullWidth={true}
                />

                {/* Microsoft OAuth Debug Panel (only shows in development or when debug enabled) */}
                <MicrosoftAuthDebugPanel />
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </ThemeProvider>
  )
}

export default LoginDefault
