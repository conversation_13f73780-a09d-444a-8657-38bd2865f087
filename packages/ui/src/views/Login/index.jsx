import React, { useEffect } from 'react'
import LoginDefault from './LoginDefault'
import { useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'

const Login = () => {
  const host = window.location.host

  const navigate = useNavigate()

  const user = useSelector((state) => state.user)
  const isLogin = user?.id ? true : false

  useEffect(() => {
    if (isLogin) {
      navigate('/')
    }
  }, [isLogin])

  if (isLogin) return <></>

  switch (host) {
    default:
      return <LoginDefault />
  }
}

export default Login
