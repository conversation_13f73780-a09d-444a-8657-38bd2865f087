import userApi from '@/api/user'
import { closeSnackbar as closeSnackbarAction, enqueueSnackbar as enqueueSnackbarAction } from '@/store/actions'
import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Grid, MenuItem, TextField } from '@mui/material'
import { createTheme, ThemeProvider } from '@mui/material/styles'
import { IconX } from '@tabler/icons-react'
import AddDocumentDialog from './AddDocumentDialog'
import PropTypes from 'prop-types'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2'
    },
    secondary: {
      main: '#dc004e'
    }
  },
  typography: {
    fontFamily: 'Roboto, Arial, sans-serif',
    h6: {
      fontWeight: 600
    },
    body1: {
      fontSize: '1rem'
    }
  },
  spacing: 8
})

const PopupEditMember = ({ open, onClose, setUserGroups, setSelectedGroup, userToEdit, selectedGroup }) => {
  const dispatch = useDispatch()
  const enqueueSnackbar = (...args) => dispatch(enqueueSnackbarAction(...args))
  const closeSnackbar = (...args) => dispatch(closeSnackbarAction(...args))
  const currentUser = useSelector((state) => state.user)

  const [editUser, setEditUser] = useState({ role: userToEdit?.role })
  // const [displayPrefixes, setDisplayPrefixes] = useState(userToEdit?.displayPrefixes || '[]')
  // const [openDialogS3, setOpenDialogS3] = useState(false)

  const handleChange = (e) => {
    const { name, value } = e.target
    setEditUser((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async () => {
    try {
      const resRegisterUser = await userApi.updateUser(userToEdit.id, { ...editUser })
      const resData = resRegisterUser?.data
      if (resData) {
        setUserGroups((prev) =>
          prev.map((item) =>
            item.groupname === userToEdit.groupname
              ? { ...item, users: item.users.map((user) => (user.id === resData.id ? resData : user)) }
              : item
          )
        )
        setSelectedGroup((prev) =>
          prev.groupname === userToEdit.groupname
            ? { ...prev, users: prev.users.map((user) => (user.id === resData.id ? resData : user)) }
            : prev
        )
        enqueueSnackbar({
          message: 'Người dùng đã được cập nhật thành công.',
          options: {
            variant: 'success',
            action: (key) => (
              <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
                <IconX />
              </Button>
            )
          }
        })
      }
      return onClose()
    } catch (error) {
      const msg = error?.response?.data?.message ? error.response.data.message : 'Cập nhật người dùng thất bại.'
      return enqueueSnackbar({
        message: msg,
        options: {
          variant: 'error',
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
    }
  }

  useEffect(() => {
    if (userToEdit) {
      setEditUser({ role: userToEdit.role })
    }
  }, [userToEdit])

  return (
    <ThemeProvider theme={theme}>
      <Dialog
        open={open}
        onClose={() => {
          onClose()
          setEditUser({ role: '' })
        }}
        fullWidth
        maxWidth='sm'
      >
        {/* <AddDocumentDialog
          open={openDialogS3}
          onClose={() => setOpenDialogS3(false)}
          displayPrefixes={displayPrefixes}
          setDisplayPrefixes={setDisplayPrefixes}
          selectedGroup={selectedGroup}
        /> */}
        <DialogTitle>Chỉnh sửa thành viên</DialogTitle>
        <DialogContent>
          <DialogContentText>Vui lòng nhập thông tin chi tiết của người dùng.</DialogContentText>
          <Grid container spacing={2}>
            {editUser && userToEdit.role !== 'MASTER_ADMIN' && (
              <Grid item xs={12}>
                <TextField
                  margin='dense'
                  name='role'
                  label='Vai trò'
                  select
                  fullWidth
                  variant='standard'
                  value={editUser.role || ''}
                  onChange={handleChange}
                >
                  {currentUser.role !== 'ADMIN' && <MenuItem value='SITE_ADMIN'>SITE_ADMIN</MenuItem>}
                  <MenuItem value='USER'>USER</MenuItem>
                </TextField>
              </Grid>
            )}
            {/* <Grid item xs={12}>
              <TextField
                margin='dense'
                name='displayPrefixes'
                label='Hiển thị tài liệu được truy cập'
                fullWidth
                variant='standard'
                value={JSON.stringify(displayPrefixes)}
                onClick={() => setOpenDialogS3(true)}
              />
            </Grid> */}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              onClose()
              setEditUser({ role: '' })
            }}
            color='secondary'
          >
            Hủy
          </Button>
          <Button onClick={handleSubmit} color='primary'>
            Đồng ý
          </Button>
        </DialogActions>
      </Dialog>
    </ThemeProvider>
  )
}

PopupEditMember.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  setUserGroups: PropTypes.func.isRequired,
  setSelectedGroup: PropTypes.func.isRequired,
  userToEdit: PropTypes.object,
  selectedGroup: PropTypes.object
}

export default PopupEditMember
