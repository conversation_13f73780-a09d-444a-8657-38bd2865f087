import userApi from '@/api/user'
import config from '@/config'
import useApi from '@/hooks/useApi'
import { closeSnackbar as closeSnackbarAction, enqueueSnackbar as enqueueSnackbarAction } from '@/store/actions'
import { Add, Delete, Edit } from '@mui/icons-material'
import {
  Button,
  CircularProgress,
  Container,
  createTheme,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  Paper,
  ThemeProvider,
  Typography
} from '@mui/material'
import { IconX } from '@tabler/icons-react'
import { useEffect, useMemo, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { IconInfoCircle } from '@tabler/icons-react'
import PopupAddGroup from './PopupAddGroup'
import PopupAddMember from './PopupAddMember'
import PopupEditMember from './PopupEditMember'
import PopupEditGroup from './PopupEditGroup'

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2'
    },
    secondary: {
      main: '#dc004e'
    }
  },
  typography: {
    h4: {
      fontWeight: 600
    },
    h6: {
      fontWeight: 500
    }
  },
  components: {
    MuiPaper: {
      styleOverrides: {
        root: {
          padding: '16px',
          marginBottom: '16px'
        }
      }
    }
  }
})

const AdminAccount = () => {
  const user = useSelector((state) => state.user)
  const isUser = user?.role === 'USER'
  const isMasterAdmin = user?.role === 'MASTER_ADMIN'
  const isSiteAdmin = user?.role === 'SITE_ADMIN'
  const isGroupAdmin = user?.role === 'ADMIN'
  const userGroupName = user?.groupname

  useEffect(() => {
    if (isUser || !user.id) {
      window.location.href = config.basename + '/'
    }
  }, [isUser, user])

  const [userGroups, setUserGroups] = useState([])
  const [selectedGroup, setSelectedGroup] = useState(null)
  const [userToEdit, setUserToEdit] = useState(null)
  const [userToDelete, setUserToDelete] = useState(null)
  const [groupToDelete, setGroupToDelete] = useState(null)
  const [groupToEdit, setGroupToEdit] = useState(null)

  const [dialogOpenAddUser, setDialogOpenAddUser] = useState(false)
  const [dialogOpenAddGroup, setDialogOpenAddGroup] = useState(false)
  const [dialogOpenEditUser, setDialogOpenEditUser] = useState(false)
  const [dialogOpenEditGroup, setDialogOpenEditGroup] = useState(false)
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)

  const dispatch = useDispatch()

  const enqueueSnackbar = (...args) => dispatch(enqueueSnackbarAction(...args))
  const closeSnackbar = (...args) => dispatch(closeSnackbarAction(...args))

  const getAllGroupUsers = useApi(userApi.getAllGroupUsers)
  const { data: dataAllGroupUsers, loading: isLoadingGetAllGroupUsers } = getAllGroupUsers

  const getUsersByGroup = useApi(userApi.getUsersByGroup)
  let { data: dataGetUsersByGroup, loading: isLoadingGetUsersByGroup } = getUsersByGroup

  const dataGroupUsers = useMemo(() => dataAllGroupUsers || dataGetUsersByGroup || [], [dataAllGroupUsers, dataGetUsersByGroup])
  const isLoading = isLoadingGetAllGroupUsers || isLoadingGetUsersByGroup

  const { loading: isLoadingDeleteGroupUser } = getAllGroupUsers
  const isLoadingDeleting = isLoadingDeleteGroupUser

  const navigate = useNavigate()

  const handleAddUser = () => {
    setDialogOpenAddUser(true)
  }

  const handleAddGroup = () => {
    setDialogOpenAddGroup(true)
  }

  const handleDialogClose = () => {
    if (dialogOpenAddGroup) setDialogOpenAddGroup(false)
    if (dialogOpenAddUser) setDialogOpenAddUser(false)
    if (dialogOpenEditUser) {
      setDialogOpenEditUser(false)
      setUserToEdit(null)
    }
    if (dialogOpenEditGroup) {
      setDialogOpenEditGroup(false)
      setGroupToEdit(null)
    }
  }

  const handleConfirmDialogClose = () => {
    setConfirmDialogOpen(false)
    setUserToDelete(null)
    setGroupToDelete(null)
  }

  const handleRemove = async () => {
    try {
      if (groupToDelete) {
        if (groupToDelete?.id) {
          await userApi.deleteGroupUser(groupToDelete?.id)
          setUserGroups((prevGroups) => prevGroups.filter((group) => group.id !== groupToDelete.id))
          if (groupToDelete.id === selectedGroup.id) {
            setSelectedGroup(userGroups.filter((group) => group.id !== groupToDelete.id)[0])
          }
        } else {
          throw new Error('Không thể xóa nhóm.')
        }
      }

      if (userToDelete) {
        if (userToDelete?.userId) {
          await userApi.removeUser(userToDelete?.userId)
          setUserGroups((prevGroups) =>
            prevGroups.map((group) =>
              group?.id === userToDelete?.groupId
                ? { ...group, users: group.users.filter((member) => member.id !== userToDelete?.userId) }
                : group
            )
          )
          setSelectedGroup((prevGroup) =>
            prevGroup.id === userToDelete?.groupId
              ? { ...prevGroup, users: prevGroup.users.filter((member) => member.id !== userToDelete?.userId) }
              : prevGroup
          )
        } else {
          throw new Error('Không thể xóa người dùng.')
        }
      }

      handleConfirmDialogClose()
      return enqueueSnackbar({
        message: 'Xóa thành công.',
        options: {
          variant: 'success',
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
    } catch (error) {
      const msg = error?.response?.data?.message ? error.response.data.message : 'Xóa không thành công.'
      return enqueueSnackbar({
        message: msg,
        options: {
          variant: 'error',
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
    }
  }

  const handleDeleteClick = async (groupId, userId, type) => {
    if (type === 'remove_group') {
      const fondGroupAdmin = userGroups.find((group) => group.id === groupId)
      if (fondGroupAdmin) {
        setGroupToDelete(fondGroupAdmin)
      }
    }
    if (type === 'remove_user') {
      setUserToDelete({ groupId, userId })
    }
    setConfirmDialogOpen(true)
  }

  useEffect(() => {
    if (isMasterAdmin) {
      getAllGroupUsers.request()
    }

    if (isGroupAdmin || isSiteAdmin) {
      getUsersByGroup.request(userGroupName)
    }
  }, [isMasterAdmin, isGroupAdmin, isSiteAdmin])

  useEffect(() => {
    if (dataGroupUsers) {
      setUserGroups(dataGroupUsers)
      setSelectedGroup(dataGroupUsers[0])
    }
  }, [dataGroupUsers])

  return (
    <ThemeProvider theme={theme}>
      <Container>
        <Typography variant='h4' gutterBottom>
          Quản lý Tài khoản Admin{' '}
        </Typography>
        <Grid container spacing={3}>
          {(isMasterAdmin || isSiteAdmin) && (
            <Grid item xs={12} md={4}>
              <Typography variant='h6' gutterBottom>
                Nhóm
              </Typography>
              {isLoading ? (
                <Grid container justifyContent='center' alignItems='center' style={{ height: '100px' }}>
                  <CircularProgress />
                </Grid>
              ) : (
                <>
                  <Paper elevation={3} style={{ maxHeight: '700px', overflowY: 'auto' }}>
                    <List>
                      {userGroups.map((group) => (
                        <ListItem
                          key={group.id}
                          onClick={() => setSelectedGroup(group)}
                          className={`cursor-pointer ${selectedGroup?.groupname === group.groupname ? 'bg-blue-100' : ''}`}
                          style={{ cursor: 'pointer' }}
                          onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#e3f2fd')}
                          onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = '')}
                        >
                          <ListItemText primary={group.groupname} />

                          <ListItemSecondaryAction>
                            {/* <IconButton
                              edge='end'
                              aria-label='info'
                              className='text-green-500 mr-1'
                              onClick={() => navigate(`/profile-group/${group.id}`)}
                            >
                              <IconInfoCircle />
                            </IconButton> */}
                            {/* <IconButton
                              edge='end'
                              aria-label='info'
                              className='text-yellow-600 mr-1'
                              onClick={(e) => {
                                e.stopPropagation()
                                setGroupToEdit(group)
                                setDialogOpenEditGroup(true)
                              }}
                            >
                              <Edit />
                            </IconButton> */}

                            <IconButton
                              edge='end'
                              aria-label='delete'
                              onClick={() => handleDeleteClick(group.id, undefined, 'remove_group')}
                              className='text-red-500'
                            >
                              <Delete />
                            </IconButton>
                          </ListItemSecondaryAction>
                        </ListItem>
                      ))}
                    </List>
                  </Paper>
                  <Button variant='contained' color='primary' startIcon={<Add />} onClick={handleAddGroup}>
                    Thêm nhóm
                  </Button>
                </>
              )}
            </Grid>
          )}
          <Grid item xs={12} md={isMasterAdmin || isSiteAdmin ? 8 : 12}>
            <Typography variant='h6' gutterBottom>
              Thành viên của nhóm {selectedGroup?.groupname}
              {/* {user.role === 'ADMIN' && (
                <IconButton
                  edge='end'
                  aria-label='info'
                  className='text-green-500 mr-1'
                  onClick={() => navigate(`/profile-group/${selectedGroup.id}`)}
                >
                  <IconInfoCircle />
                </IconButton>
              )} */}
            </Typography>
            {isLoading ? (
              <Grid container justifyContent='center' alignItems='center' style={{ height: '100px' }}>
                <CircularProgress />
              </Grid>
            ) : (
              <>
                <Paper elevation={3} style={{ maxHeight: '700px', overflowY: 'auto' }}>
                  <List>
                    {selectedGroup?.users.map((member) => (
                      <ListItem key={member.id}>
                        <ListItemText
                          primary={`${member.username}`}
                          secondary={`Vai trò: ${member.groupname === 'Master_admin' ? 'MASTER_ADMIN' : member.role}, Email: ${
                            member.email
                          }`}
                        />
                        <div className='flex items-center gap-1 w-[55px] justify-end'>
                          {/* <IconButton
                            edge='start'
                            aria-label='info'
                            className='text-green-500 mr-2'
                            onClick={() => navigate(`/profile/${member.id}`)}
                          >
                            <IconInfoCircle />
                          </IconButton> */}

                          {selectedGroup?.groupname !== 'Master_admin' && (
                            <IconButton
                              edge='start'
                              aria-label='edit'
                              className='text-yellow-600'
                              onClick={(e) => {
                                e.stopPropagation()
                                setUserToEdit(member)
                                setDialogOpenEditUser(true)
                              }}
                            >
                              <Edit />
                            </IconButton>
                          )}

                          {(isMasterAdmin || isGroupAdmin || isSiteAdmin) && (
                            <IconButton
                              edge='end'
                              aria-label='delete'
                              onClick={(e) => {
                                e.stopPropagation()
                                handleDeleteClick(selectedGroup.id, member.id, 'remove_user')
                              }}
                              className='text-red-500'
                            >
                              <Delete />
                            </IconButton>
                          )}
                        </div>
                      </ListItem>
                    ))}
                  </List>
                </Paper>
                {(isMasterAdmin || isSiteAdmin || isGroupAdmin) && (
                  <Button variant='contained' color='primary' startIcon={<Add />} onClick={handleAddUser}>
                    Thêm thành viên
                  </Button>
                )}
              </>
            )}
          </Grid>
        </Grid>

        <PopupAddGroup
          open={dialogOpenAddGroup}
          onClose={handleDialogClose}
          setUserGroups={setUserGroups}
          parentGroup={isSiteAdmin ? (selectedGroup?.parentGroup ? selectedGroup.parentGroup : selectedGroup) : null}
        />
        {userToEdit && (
          <PopupEditMember
            open={dialogOpenEditUser}
            onClose={handleDialogClose}
            setSelectedGroup={setSelectedGroup}
            setUserGroups={setUserGroups}
            userToEdit={userToEdit}
            selectedGroup={selectedGroup}
          />
        )}
        {groupToEdit && (
          <PopupEditGroup
            open={dialogOpenEditGroup}
            onClose={handleDialogClose}
            setSelectedGroup={setSelectedGroup}
            setUserGroups={setUserGroups}
            groupToEdit={groupToEdit}
            selectedGroup={selectedGroup}
          />
        )}
        {selectedGroup && (
          <PopupAddMember
            open={dialogOpenAddUser}
            onClose={handleDialogClose}
            userGroups={userGroups}
            selectedGroup={selectedGroup}
            setUserGroups={setUserGroups}
            setSelectedGroup={setSelectedGroup}
          />
        )}
        <Dialog
          open={confirmDialogOpen}
          onClose={handleConfirmDialogClose}
          aria-labelledby='confirm-dialog-title'
          aria-describedby='confirm-dialog-description'
        >
          <DialogTitle id='confirm-dialog-title'>Xác nhận Xóa</DialogTitle>
          <DialogContent>
            <DialogContentText id='confirm-dialog-description'>
              Bạn có chắc chắn muốn xóa {groupToDelete ? 'nhóm' : 'người dùng'} này?
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleConfirmDialogClose} color='primary'>
              Hủy
            </Button>
            <Button
              onClick={handleRemove}
              color='secondary'
              className='bg-red-500 text-white hover:bg-red-300'
              disabled={isLoadingDeleting}
            >
              Đồng ý
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </ThemeProvider>
  )
}

export default AdminAccount
