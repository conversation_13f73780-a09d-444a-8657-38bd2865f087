import React, { useEffect, useState } from 'react'
import { Box, CircularProgress, Typography, Alert } from '@mui/material'
import { useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { loginAction } from '@/store/actions'
import useMSAL from '@/hooks/useMSAL'
import userApi from '@/api/user'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'

const MicrosoftRedirect = () => {
  const [status, setStatus] = useState('processing') // processing, success, error
  const [message, setMessage] = useState('Đang xử lý đăng nhập Microsoft...')
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { handleRedirectResult, getAccessToken, isInitialized } = useMSAL()

  const login = (...args) => dispatch(loginAction(...args))

  useEffect(() => {
    const processRedirect = async () => {
      try {
        microsoftOAuthDebugger.info('Microsoft redirect page loaded')
        microsoftOAuthDebugger.debugUrlParams()

        if (!isInitialized) {
          microsoftOAuthDebugger.warning('MSAL not initialized, waiting...')
          return
        }

        setMessage('Đang xử lý phản hồi từ Microsoft...')
        
        // Handle the redirect result
        const result = await handleRedirectResult()
        
        if (!result) {
          microsoftOAuthDebugger.warning('No redirect result found')
          setStatus('error')
          setMessage('Không tìm thấy kết quả đăng nhập từ Microsoft. Vui lòng thử lại.')
          setTimeout(() => navigate('/login'), 3000)
          return
        }

        microsoftOAuthDebugger.success('Microsoft redirect result received', {
          account: result.account?.username,
          hasAccessToken: !!result.accessToken
        })

        setMessage('Đang xác thực với hệ thống...')

        // Get access token
        const accessToken = result.accessToken || await getAccessToken()
        microsoftOAuthDebugger.debugToken(accessToken, 'access')

        // Send access token to backend for validation and user creation/login
        const response = await userApi.loginWithMicrosoft({ accessToken })
        
        if (response.data && response.data.success) {
          microsoftOAuthDebugger.success('Backend authentication successful', {
            userId: response.data.user?.id,
            username: response.data.user?.username
          })

          // Store login data
          const loginData = {
            user: response.data.user,
            accessToken: response.data.accessToken,
            refreshToken: response.data.refreshToken
          }

          localStorage.setItem('dataLogin', JSON.stringify(loginData))
          
          // Clear specified localStorage items after successful login
          localStorage.removeItem('chatInputHistory')
          localStorage.removeItem('57e4146c-dd6b-4eed-ae49-40223b00af25_EXTERNAL')
          localStorage.removeItem('248e6488-33f3-4b91-a75f-a0e1f76f286c_EXTERNAL')

          // Update Redux state
          login(response.data.user)

          setStatus('success')
          setMessage('Đăng nhập thành công! Đang chuyển hướng...')

          // Redirect to the intended page or home
          setTimeout(() => {
            const redirectUrl = localStorage.getItem('redirectAfterLogin')
            if (redirectUrl) {
              localStorage.removeItem('redirectAfterLogin')
              window.location.href = redirectUrl
            } else {
              navigate('/')
            }
          }, 1500)
        } else {
          throw new Error(response.data?.message || 'Backend authentication failed')
        }
      } catch (error) {
        microsoftOAuthDebugger.error('Microsoft redirect processing failed', {
          error: error.message,
          response: error.response?.data,
          status: error.response?.status
        })

        setStatus('error')
        
        let errorMessage = 'Đăng nhập Microsoft thất bại. Vui lòng thử lại.'
        
        if (error.response?.status === 401) {
          errorMessage = 'Token Microsoft không hợp lệ hoặc đã hết hạn.'
        } else if (error.response?.status === 403) {
          errorMessage = 'Tài khoản không có quyền truy cập.'
        } else if (error.response?.data?.message) {
          errorMessage = error.response.data.message
        } else if (error.message.includes('Network Error')) {
          errorMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.'
        }
        
        setMessage(errorMessage)
        
        // Redirect to login page after 5 seconds
        setTimeout(() => navigate('/login'), 5000)
      }
    }

    if (isInitialized) {
      processRedirect()
    }
  }, [isInitialized, handleRedirectResult, getAccessToken, navigate, login])

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'success'
      case 'error':
        return 'error'
      default:
        return 'info'
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <CircularProgress size={40} sx={{ mb: 2 }} />
      case 'success':
        return <Typography variant="h3" sx={{ mb: 2, color: 'success.main' }}>✓</Typography>
      case 'error':
        return <Typography variant="h3" sx={{ mb: 2, color: 'error.main' }}>✗</Typography>
      default:
        return null
    }
  }

  return (
    <Box
      sx={{
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        p: 3,
        backgroundColor: '#f5f5f5'
      }}
    >
      <Box
        sx={{
          textAlign: 'center',
          backgroundColor: 'white',
          borderRadius: 2,
          p: 4,
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          maxWidth: 400,
          width: '100%'
        }}
      >
        {getStatusIcon()}
        
        <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
          Microsoft OAuth
        </Typography>
        
        <Alert severity={getStatusColor()} sx={{ mb: 2 }}>
          {message}
        </Alert>
        
        {status === 'error' && (
          <Typography variant="body2" color="text.secondary">
            Bạn sẽ được chuyển về trang đăng nhập trong 5 giây...
          </Typography>
        )}
        
        {status === 'success' && (
          <Typography variant="body2" color="text.secondary">
            Đang chuyển hướng...
          </Typography>
        )}
      </Box>
    </Box>
  )
}

export default MicrosoftRedirect
