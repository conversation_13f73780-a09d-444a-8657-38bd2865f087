import React, { useEffect, useState } from 'react'
import { Box, CircularProgress, Typography, Alert } from '@mui/material'
import { useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { loginAction } from '@/store/actions'
import useMS<PERSON> from '@/hooks/useMSAL'
import userApi from '@/api/user'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'
import microsoftAuthStorage from '@/utils/microsoftAuthStorage'
import MicrosoftAuthDebugPanel from '@/components/MicrosoftAuthDebugPanel'

const MicrosoftRedirect = () => {
  const [status, setStatus] = useState('processing') // processing, success, error
  const [errorMessage, setErrorMessage] = useState('')
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const login = (...args) => dispatch(loginAction(...args))
  
  const { isInitialized, handleRedirectPromise } = useMSAL()

  useEffect(() => {
    const handleMicrosoftRedirect = async () => {
      if (!isInitialized) {
        microsoftOAuthDebugger.info('MSAL not initialized yet, waiting...')
        return
      }

      try {
        microsoftOAuthDebugger.info('Microsoft redirect page loaded, handling redirect promise')
        microsoftOAuthDebugger.info('Current URL:', window.location.href)
        microsoftOAuthDebugger.info('URL search params:', window.location.search)

        // Lưu authorization code vào localStorage nếu có
        const urlParams = new URLSearchParams(window.location.search)
        const authCode = urlParams.get('code')
        const state = urlParams.get('state')
        const error = urlParams.get('error')
        const errorDescription = urlParams.get('error_description')

        if (authCode) {
          // Sử dụng storage utility để lưu authorization code
          microsoftAuthStorage.saveAuthCode(authCode, state, {
            sessionId: urlParams.get('session_state'),
            correlationId: urlParams.get('correlation_id')
          })

          microsoftOAuthDebugger.success('Authorization code saved to localStorage using storage utility', {
            codeLength: authCode.length,
            codePreview: authCode.substring(0, 20) + '...',
            state: state
          })
        }

        if (error) {
          // Sử dụng storage utility để lưu lỗi OAuth
          microsoftAuthStorage.saveAuthError(error, errorDescription, {
            sessionId: urlParams.get('session_state'),
            correlationId: urlParams.get('correlation_id')
          })

          microsoftOAuthDebugger.error('OAuth error found in URL and saved to storage', { error, errorDescription })
        }

        setStatus('processing')

        // Handle the redirect response from Microsoft
        const response = await handleRedirectPromise()

        microsoftOAuthDebugger.info('MSAL handleRedirectPromise response:', response)

        if (!response) {
          microsoftOAuthDebugger.warning('No redirect response received from MSAL')
          // Check if we have URL parameters that indicate an error
          const urlParams = new URLSearchParams(window.location.search)
          const error = urlParams.get('error')
          const errorDescription = urlParams.get('error_description')

          if (error) {
            microsoftOAuthDebugger.error('OAuth error in URL:', { error, errorDescription })
            setStatus('error')
            setErrorMessage(`Lỗi OAuth: ${errorDescription || error}`)
            return
          }

          setStatus('error')
          setErrorMessage('Không nhận được phản hồi từ Microsoft. Vui lòng thử lại.')
          return
        }

        const accessToken = response.accessToken
        if (!accessToken) {
          microsoftOAuthDebugger.error('No access token in MSAL response')
          setStatus('error')
          setErrorMessage('Không nhận được access token từ Microsoft. Vui lòng thử lại.')
          return
        }

        // Lưu access token vào localStorage
        microsoftAuthStorage.saveAccessToken(accessToken, response.expiresOn, {
          account: response.account?.username,
          accountId: response.account?.homeAccountId,
          scopes: response.scopes,
          tokenType: response.tokenType
        })

        microsoftOAuthDebugger.success('Access token received from MSAL and saved to storage', {
          tokenLength: accessToken.length,
          tokenPreview: accessToken.substring(0, 20) + '...',
          account: response.account?.username,
          expiresOn: response.expiresOn
        })

        // Send access token to backend for validation and user creation/login
        microsoftOAuthDebugger.info('Sending access token to backend API')

        try {
          const loginResponse = await userApi.loginWithMicrosoft({ accessToken })
          microsoftOAuthDebugger.info('Backend API response:', {
            status: loginResponse.status,
            statusText: loginResponse.statusText,
            data: loginResponse.data
          })

          if (loginResponse.data && loginResponse.data.user) {
          microsoftOAuthDebugger.success('Backend login successful', {
            userId: loginResponse.data.user.id,
            username: loginResponse.data.user.username,
            hasTokens: !!(loginResponse.data.accessToken && loginResponse.data.refreshToken)
          })

          // Store login data
          localStorage.setItem('dataLogin', JSON.stringify(loginResponse.data))
          
          // Update Redux store
          login(loginResponse.data.user)
          
          setStatus('success')
          
          // Redirect to the original page or home
          const redirectPath = localStorage.getItem('redirectAfterLogin') || '/'
          localStorage.removeItem('redirectAfterLogin')
          
          microsoftOAuthDebugger.success('Redirecting to application', { redirectPath })
          
          setTimeout(() => {
            navigate(redirectPath)
          }, 1000)
        } else {
          microsoftOAuthDebugger.error('Invalid response from backend API', {
            response: loginResponse,
            hasData: !!loginResponse.data,
            hasUser: !!loginResponse.data?.user,
            dataKeys: loginResponse.data ? Object.keys(loginResponse.data) : []
          })
          setStatus('error')
          setErrorMessage('Phản hồi không hợp lệ từ server. Vui lòng thử lại.')
        }
        } catch (apiError) {
          microsoftOAuthDebugger.error('Backend API call failed', {
            error: apiError.message,
            response: apiError.response?.data,
            status: apiError.response?.status,
            statusText: apiError.response?.statusText,
            url: apiError.config?.url,
            method: apiError.config?.method
          })

          setStatus('error')
          if (apiError.response?.data?.message) {
            setErrorMessage(`Lỗi server: ${apiError.response.data.message}`)
          } else if (apiError.response?.status === 404) {
            setErrorMessage('Endpoint Microsoft OAuth không tìm thấy. Vui lòng kiểm tra cấu hình server.')
          } else if (apiError.response?.status >= 500) {
            setErrorMessage('Lỗi server nội bộ. Vui lòng thử lại sau.')
          } else {
            setErrorMessage(`Lỗi kết nối API: ${apiError.message}`)
          }
        }
      } catch (error) {
        microsoftOAuthDebugger.error('Error during Microsoft redirect handling', {
          error: error.message,
          errorType: error.constructor.name,
          response: error.response?.data,
          stack: error.stack
        })

        setStatus('error')

        if (error.response?.data?.message) {
          setErrorMessage(error.response.data.message)
        } else if (error.message) {
          setErrorMessage(`Lỗi: ${error.message}`)
        } else {
          setErrorMessage('Đã có lỗi xảy ra trong quá trình đăng nhập Microsoft. Vui lòng thử lại.')
        }
      }
    }

    handleMicrosoftRedirect()
  }, [isInitialized, handleRedirectPromise, navigate, login])

  const handleRetry = () => {
    microsoftOAuthDebugger.info('User clicked retry, redirecting to login page')
    navigate('/login')
  }

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="100vh"
      padding={3}
    >
      {status === 'processing' && (
        <>
          <CircularProgress size={60} sx={{ mb: 3 }} />
          <Typography variant="h6" gutterBottom>
            Đang xử lý đăng nhập Microsoft...
          </Typography>
          <Typography variant="body2" color="text.secondary" textAlign="center">
            Vui lòng đợi trong khi chúng tôi xác thực thông tin của bạn
          </Typography>
        </>
      )}

      {status === 'success' && (
        <>
          <Box
            sx={{
              width: 60,
              height: 60,
              borderRadius: '50%',
              backgroundColor: 'success.main',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mb: 3
            }}
          >
            <Typography variant="h4" color="white">
              ✓
            </Typography>
          </Box>
          <Typography variant="h6" gutterBottom color="success.main">
            Đăng nhập thành công!
          </Typography>
          <Typography variant="body2" color="text.secondary" textAlign="center">
            Đang chuyển hướng đến ứng dụng...
          </Typography>
        </>
      )}

      {status === 'error' && (
        <>
          <Alert severity="error" sx={{ mb: 3, maxWidth: 500 }}>
            <Typography variant="body1" gutterBottom>
              Đăng nhập Microsoft thất bại
            </Typography>
            <Typography variant="body2">
              {errorMessage}
            </Typography>
          </Alert>

          {/* Debug Information */}
          <Box sx={{ mb: 2, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1, maxWidth: 500 }}>
            <Typography variant="caption" display="block" gutterBottom>
              Debug Info (chỉ hiển thị khi có lỗi):
            </Typography>
            <Typography variant="caption" display="block">
              URL: {window.location.href}
            </Typography>
            <Typography variant="caption" display="block">
              Search: {window.location.search}
            </Typography>
            <Typography variant="caption" display="block">
              MSAL Initialized: {isInitialized ? 'Yes' : 'No'}
            </Typography>
            <Typography variant="caption" display="block">
              Debug Mode: {localStorage.getItem('microsoft-oauth-debug') === 'true' ? 'Enabled' : 'Disabled'}
            </Typography>
            <Typography variant="caption" display="block">
              Auth Code: {microsoftAuthStorage.hasAuthCode() ? 'Có' : 'Không'}
            </Typography>
            <Typography variant="caption" display="block">
              Access Token: {microsoftAuthStorage.hasValidAccessToken() ? 'Có' : 'Không'}
            </Typography>
          </Box>

          <Typography
            variant="body2"
            color="primary"
            sx={{ cursor: 'pointer', textDecoration: 'underline', mb: 1 }}
            onClick={handleRetry}
          >
            Thử lại
          </Typography>

          <Typography
            variant="body2"
            color="secondary"
            sx={{ cursor: 'pointer', textDecoration: 'underline', mb: 1 }}
            onClick={() => {
              localStorage.setItem('microsoft-oauth-debug', 'true')
              alert('Debug mode enabled! Check console for detailed logs.')
            }}
          >
            Bật debug mode
          </Typography>

          <Typography
            variant="body2"
            color="secondary"
            sx={{ cursor: 'pointer', textDecoration: 'underline' }}
            onClick={() => {
              const data = microsoftAuthStorage.debugAll()
              console.log('🔐 All Microsoft OAuth Storage Data:', data)
              alert('Dữ liệu localStorage đã được in ra console. Mở F12 để xem.')
            }}
          >
            Xem dữ liệu localStorage
          </Typography>
        </>
      )}

      {/* Debug Panel */}
      <MicrosoftAuthDebugPanel />
    </Box>
  )
}

export default MicrosoftRedirect
