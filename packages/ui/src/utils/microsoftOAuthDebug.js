// Microsoft OAuth2 Simple Logger
class MicrosoftOAuthDebugger {
  constructor() {
    this.isEnabled = process.env.NODE_ENV === 'development'
  }

  log(message, data = null, level = 'info') {
    if (!this.isEnabled) return

    const emoji = {
      info: '🔍',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    }

    const consoleMethod = level === 'error' ? 'error' : level === 'warning' ? 'warn' : 'log'
    const logMessage = `${emoji[level]} [Microsoft OAuth]: ${message}`

    if (data) {
      console[consoleMethod](logMessage, data)
    } else {
      console[consoleMethod](logMessage)
    }
  }

  info(message, data = null) {
    this.log(message, data, 'info')
  }

  success(message, data = null) {
    this.log(message, data, 'success')
  }

  warning(message, data = null) {
    this.log(message, data, 'warning')
  }

  error(message, data = null) {
    this.log(message, data, 'error')
  }
}

// Create global instance
const microsoftOAuthDebugger = new MicrosoftOAuthDebugger()

export default microsoftOAuthDebugger
