// Microsoft OAuth2 Debug Utility
class MicrosoftOAuthDebugger {
  constructor() {
    this.logs = []
    this.isEnabled = process.env.NODE_ENV === 'development' || localStorage.getItem('microsoft-oauth-debug') === 'true'
  }

  log(message, data = null, level = 'info') {
    if (!this.isEnabled) return

    const timestamp = new Date().toISOString()
    const logEntry = {
      timestamp,
      level,
      message,
      data,
      url: window.location.href,
      userAgent: navigator.userAgent
    }

    this.logs.push(logEntry)

    // Console output with emoji
    const emoji = {
      info: '🔍',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      debug: '🐛'
    }

    const consoleMethod = level === 'error' ? 'error' : level === 'warning' ? 'warn' : 'log'
    const logMessage = `${emoji[level]} [Microsoft OAuth Debug] [${timestamp}]: ${message}`
    
    if (data) {
      console[consoleMethod](logMessage, data)
    } else {
      console[consoleMethod](logMessage)
    }

    // Keep only last 100 logs
    if (this.logs.length > 100) {
      this.logs = this.logs.slice(-100)
    }
  }

  info(message, data = null) {
    this.log(message, data, 'info')
  }

  success(message, data = null) {
    this.log(message, data, 'success')
  }

  warning(message, data = null) {
    this.log(message, data, 'warning')
  }

  error(message, data = null) {
    this.log(message, data, 'error')
  }

  debug(message, data = null) {
    this.log(message, data, 'debug')
  }

  // Get all logs
  getLogs() {
    return this.logs
  }

  // Clear all logs
  clearLogs() {
    this.logs = []
    this.info('Debug logs cleared')
  }

  // Export logs as JSON
  exportLogs() {
    const logsJson = JSON.stringify(this.logs, null, 2)
    const blob = new Blob([logsJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `microsoft-oauth-debug-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    this.info('Debug logs exported')
  }

  // Debug MSAL configuration
  debugMSALConfig(config) {
    this.info('MSAL Configuration', {
      clientId: config.auth.clientId,
      authority: config.auth.authority,
      redirectUri: config.auth.redirectUri,
      cacheLocation: config.cache.cacheLocation,
      storeAuthStateInCookie: config.cache.storeAuthStateInCookie
    })
  }

  // Debug token information
  debugToken(token, type = 'access') {
    if (!token) {
      this.warning(`${type} token is null or undefined`)
      return
    }

    this.info(`${type} Token Debug`, {
      type,
      length: token.length,
      preview: token.substring(0, 20) + '...',
      hasBearer: token.startsWith('Bearer '),
      isJWT: token.split('.').length === 3
    })
  }

  // Debug URL parameters
  debugUrlParams() {
    const urlParams = new URLSearchParams(window.location.search)
    const params = {}
    for (const [key, value] of urlParams) {
      params[key] = value
    }
    
    this.info('URL Parameters', {
      url: window.location.href,
      search: window.location.search,
      params
    })
  }

  // Debug user information
  debugUser(user) {
    if (!user) {
      this.warning('User object is null or undefined')
      return
    }

    this.info('User Information', {
      id: user.id,
      username: user.username,
      email: user.email,
      displayName: user.displayName,
      role: user.role,
      active: user.active
    })
  }

  // Debug API response
  debugApiResponse(response, endpoint) {
    this.info(`API Response: ${endpoint}`, {
      status: response.status,
      statusText: response.statusText,
      hasData: !!response.data,
      dataKeys: response.data ? Object.keys(response.data) : []
    })
  }

  // Debug API error
  debugApiError(error, endpoint) {
    this.error(`API Error: ${endpoint}`, {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    })
  }

  // Get logs summary
  getLogsSummary() {
    const summary = {
      total: this.logs.length,
      byLevel: {},
      timeRange: {
        start: this.logs[0]?.timestamp,
        end: this.logs[this.logs.length - 1]?.timestamp
      }
    }
    
    this.logs.forEach(log => {
      summary.byLevel[log.level] = (summary.byLevel[log.level] || 0) + 1
    })
    
    return summary
  }

  // Enable/disable debug mode
  setEnabled(enabled) {
    this.isEnabled = enabled
    localStorage.setItem('microsoft-oauth-debug', enabled.toString())
    this.info(`Debug mode ${enabled ? 'enabled' : 'disabled'}`)
  }
}

// Create global instance
const microsoftOAuthDebugger = new MicrosoftOAuthDebugger()

// Expose to window for debugging
if (typeof window !== 'undefined') {
  window.microsoftOAuthDebugger = microsoftOAuthDebugger
}

export default microsoftOAuthDebugger
