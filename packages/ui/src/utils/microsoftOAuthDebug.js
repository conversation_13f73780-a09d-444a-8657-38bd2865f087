// Microsoft OAuth2 Debug Utility
class MicrosoftOAuthDebugger {
  constructor() {
    this.logs = []
    this.isEnabled = process.env.NODE_ENV === 'development' || localStorage.getItem('microsoft-oauth-debug') === 'true'
  }

  log(message, data = null, level = 'info') {
    if (!this.isEnabled) return

    const timestamp = new Date().toISOString()
    const logEntry = {
      timestamp,
      level,
      message,
      data,
      url: window.location.href,
      userAgent: navigator.userAgent
    }

    this.logs.push(logEntry)

    // Console output with emoji
    const emoji = {
      info: '🔍',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      debug: '🐛'
    }

    const consoleMethod = level === 'error' ? 'error' : level === 'warning' ? 'warn' : 'log'
    const logMessage = `${emoji[level]} [Microsoft OAuth Debug] [${timestamp}]: ${message}`
    
    if (data) {
      console[consoleMethod](logMessage, data)
    } else {
      console[consoleMethod](logMessage)
    }

    // Keep only last 100 logs
    if (this.logs.length > 100) {
      this.logs = this.logs.slice(-100)
    }
  }

  info(message, data = null) {
    this.log(message, data, 'info')
  }

  success(message, data = null) {
    this.log(message, data, 'success')
  }

  warning(message, data = null) {
    this.log(message, data, 'warning')
  }

  error(message, data = null) {
    this.log(message, data, 'error')
  }

  debug(message, data = null) {
    this.log(message, data, 'debug')
  }

  // Debug MSAL configuration
  debugMSALConfig(config) {
    this.info('MSAL Configuration', {
      clientId: config.auth.clientId,
      authority: config.auth.authority,
      redirectUri: config.auth.redirectUri,
      cacheLocation: config.cache.cacheLocation,
      storeAuthStateInCookie: config.cache.storeAuthStateInCookie
    })
  }

  // Debug Microsoft Graph API response
  debugGraphResponse(response) {
    this.info('Microsoft Graph API Response', {
      id: response.id,
      displayName: response.displayName,
      mail: response.mail,
      userPrincipalName: response.userPrincipalName,
      givenName: response.givenName,
      surname: response.surname
    })
  }

  // Debug backend API response
  debugBackendResponse(response) {
    this.info('Backend API Response', {
      status: response.status,
      statusText: response.statusText,
      hasData: !!response.data,
      hasUser: !!response.data?.user,
      hasTokens: !!(response.data?.accessToken && response.data?.refreshToken)
    })
  }

  // Debug authentication flow
  debugAuthFlow(step, data = null) {
    this.info(`Auth Flow - ${step}`, data)
  }

  // Debug token information
  debugToken(token, type = 'access') {
    if (!token) {
      this.warning(`${type} token is null or undefined`)
      return
    }

    this.info(`${type} Token Debug`, {
      type,
      length: token.length,
      preview: token.substring(0, 20) + '...',
      hasBearer: token.startsWith('Bearer '),
      isJWT: token.split('.').length === 3
    })
  }

  // Debug URL parameters
  debugUrlParams() {
    const urlParams = new URLSearchParams(window.location.search)
    const params = {}
    for (const [key, value] of urlParams) {
      params[key] = value
    }
    
    this.info('URL Parameters', {
      url: window.location.href,
      search: window.location.search,
      params
    })
  }

  // Debug local storage
  debugLocalStorage() {
    const relevantKeys = ['dataLogin', 'microsoft-oauth-debug']
    const storage = {}
    
    relevantKeys.forEach(key => {
      const value = localStorage.getItem(key)
      if (value) {
        try {
          storage[key] = JSON.parse(value)
        } catch {
          storage[key] = value
        }
      }
    })
    
    this.info('Local Storage Debug', storage)
  }

  // Get all logs
  getLogs() {
    return this.logs
  }

  // Clear logs
  clearLogs() {
    this.logs = []
    this.info('Logs cleared')
  }

  // Export logs as JSON
  exportLogs() {
    const logsJson = JSON.stringify(this.logs, null, 2)
    const blob = new Blob([logsJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `microsoft-oauth-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    this.info('Logs exported to file')
  }

  // Get logs summary
  getLogsSummary() {
    const summary = {
      total: this.logs.length,
      byLevel: {},
      timeRange: {
        start: this.logs[0]?.timestamp,
        end: this.logs[this.logs.length - 1]?.timestamp
      }
    }
    
    this.logs.forEach(log => {
      summary.byLevel[log.level] = (summary.byLevel[log.level] || 0) + 1
    })
    
    return summary
  }

  // Enable/disable debug mode
  setEnabled(enabled) {
    this.isEnabled = enabled
    localStorage.setItem('microsoft-oauth-debug', enabled.toString())
    this.info(`Debug mode ${enabled ? 'enabled' : 'disabled'}`)
  }
}

// Create global instance
const microsoftOAuthDebugger = new MicrosoftOAuthDebugger()

// Expose to window for debugging
if (typeof window !== 'undefined') {
  window.microsoftOAuthDebugger = microsoftOAuthDebugger
}

export default microsoftOAuthDebugger
