import { useState, useEffect, useRef } from 'react'
import { PublicClientApplication } from '@azure/msal-browser'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'

const useMSAL = () => {
  const [msalInstance, setMsalInstance] = useState(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState(null)
  const [isInteractionInProgress, setIsInteractionInProgress] = useState(false)
  const interactionInProgressRef = useRef(false)

  useEffect(() => {
    const initializeMSAL = async () => {
      try {
        // MSAL Configuration
        // Check if Microsoft OAuth is properly configured
        const clientId = 'b9dc830e-69bf-426d-a1ef-d349d5580a57'
        if (!clientId || clientId === 'your-microsoft-client-id') {
          throw new Error('Microsoft OAuth is not configured. Please set REACT_APP_MICROSOFT_CLIENT_ID in your .env file.')
        }

        const msalConfig = {
          auth: {
            clientId: clientId,
            authority: process.env.REACT_APP_MICROSOFT_TENANT_ID
              ? `https://login.microsoftonline.com/${process.env.REACT_APP_MICROSOFT_TENANT_ID}`
              : 'https://login.microsoftonline.com/common',
            redirectUri: window.location.origin + '/redirect'
          },
          cache: {
            cacheLocation: 'localStorage',
            storeAuthStateInCookie: false
          }
        }

        microsoftOAuthDebugger.debugMSALConfig(msalConfig)
        microsoftOAuthDebugger.info('Creating MSAL instance')
        
        const instance = new PublicClientApplication(msalConfig)
        setMsalInstance(instance)
        
        microsoftOAuthDebugger.info('Initializing MSAL instance')
        await instance.initialize()
        
        microsoftOAuthDebugger.success('MSAL instance initialized successfully')
        setIsInitialized(true)
        setError(null)
      } catch (err) {
        microsoftOAuthDebugger.error('Failed to initialize MSAL', {
          error: err.message,
          errorType: err.constructor.name
        })
        setError(err)
        setIsInitialized(false)
      }
    }

    initializeMSAL()
  }, [])

  // Monitor interaction status - simplified approach
  useEffect(() => {
    if (!msalInstance || !isInitialized) return

    // Simple timeout to reset interaction flag after 30 seconds
    const resetInteractionFlag = () => {
      if (interactionInProgressRef.current) {
        setTimeout(() => {
          microsoftOAuthDebugger.info('Resetting interaction flag after timeout')
          setIsInteractionInProgress(false)
          interactionInProgressRef.current = false
        }, 30000) // 30 seconds timeout
      }
    }

    resetInteractionFlag()
  }, [msalInstance, isInitialized, isInteractionInProgress])

  const login = async (scopes = ['User.Read']) => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }

    try {
      // First, try to handle any existing redirect result
      const redirectResult = await msalInstance.handleRedirectPromise()
      if (redirectResult) {
        microsoftOAuthDebugger.info('Found existing redirect result, clearing it')
      }

      // Check if there are any active accounts
      const accounts = msalInstance.getAllAccounts()
      if (accounts.length > 0) {
        microsoftOAuthDebugger.info('Found existing accounts, attempting silent token acquisition')
        try {
          const silentRequest = {
            scopes,
            account: accounts[0]
          }
          const result = await msalInstance.acquireTokenSilent(silentRequest)
          microsoftOAuthDebugger.success('Silent token acquisition successful')
          return result
        } catch (silentError) {
          microsoftOAuthDebugger.warning('Silent token acquisition failed, proceeding with interactive login', silentError)
        }
      }

      // Prevent multiple simultaneous login attempts
      if (isInteractionInProgress || interactionInProgressRef.current) {
        microsoftOAuthDebugger.warning('Interaction already in progress, loginRedirect blocked')
        throw new Error('Đang có phiên đăng nhập Microsoft khác đang diễn ra. Vui lòng chờ...')
      }

      const loginRequest = { scopes }
      microsoftOAuthDebugger.info('Initiating Microsoft login redirect', loginRequest)
      setIsInteractionInProgress(true)
      interactionInProgressRef.current = true

      // Use loginRedirect which doesn't return a promise in some MSAL versions
      await msalInstance.loginRedirect(loginRequest)
      microsoftOAuthDebugger.success('MSAL login redirect initiated successfully')

    } catch (err) {
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false

      // Handle specific MSAL errors
      if (err.errorCode === 'interaction_in_progress') {
        microsoftOAuthDebugger.warning('Interaction already in progress, clearing state and retrying')
        // Clear any stuck interaction state
        setTimeout(() => {
          setIsInteractionInProgress(false)
          interactionInProgressRef.current = false
        }, 2000)
        throw new Error('Có phiên đăng nhập Microsoft đang diễn ra. Vui lòng đợi 2 giây và thử lại.')
      }

      microsoftOAuthDebugger.error('Error during Microsoft login initiation', {
        error: err.message,
        errorCode: err.errorCode,
        errorType: err.constructor.name
      })
      throw err
    }
  }

  const handleRedirectResult = async () => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }
    try {
      microsoftOAuthDebugger.info('Handling redirect result')
      const result = await msalInstance.handleRedirectPromise()
      
      if (result) {
        microsoftOAuthDebugger.success('Redirect result handled successfully', {
          account: result.account?.username,
          hasAccessToken: !!result.accessToken
        })
        return result
      } else {
        microsoftOAuthDebugger.info('No redirect result found')
        return null
      }
    } catch (err) {
      microsoftOAuthDebugger.error('Error handling redirect result', {
        error: err.message,
        errorType: err.constructor.name
      })
      throw err
    }
  }

  const getAccessToken = async (scopes = ['User.Read']) => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }
    try {
      const accounts = msalInstance.getAllAccounts()
      if (accounts.length === 0) {
        throw new Error('No accounts found')
      }

      const request = {
        scopes,
        account: accounts[0]
      }

      microsoftOAuthDebugger.info('Acquiring access token silently', request)
      const result = await msalInstance.acquireTokenSilent(request)
      microsoftOAuthDebugger.success('Access token acquired successfully')
      return result.accessToken
    } catch (err) {
      microsoftOAuthDebugger.error('Error acquiring access token', {
        error: err.message,
        errorType: err.constructor.name
      })
      throw err
    }
  }

  const logout = async () => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }
    try {
      microsoftOAuthDebugger.info('Logging out from Microsoft')
      // Use logoutRedirect which might not return a promise in some MSAL versions
      msalInstance.logoutRedirect()
      microsoftOAuthDebugger.success('Logout initiated successfully')
    } catch (err) {
      microsoftOAuthDebugger.error('Error during logout', {
        error: err.message,
        errorType: err.constructor.name
      })
      throw err
    }
  }

  const getActiveAccount = () => {
    if (!isInitialized || !msalInstance) {
      return null
    }
    return msalInstance.getActiveAccount()
  }

  const clearInteractionState = () => {
    microsoftOAuthDebugger.info('Manually clearing interaction state')
    setIsInteractionInProgress(false)
    interactionInProgressRef.current = false
  }

  return {
    msalInstance,
    isInitialized,
    error,
    isInteractionInProgress,
    login,
    logout,
    handleRedirectResult,
    getAccessToken,
    getActiveAccount,
    clearInteractionState
  }
}

export default useMSAL
