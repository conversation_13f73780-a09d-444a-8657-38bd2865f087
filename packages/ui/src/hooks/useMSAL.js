import { useState, useEffect, useRef } from 'react'
import { PublicClientApplication, InteractionStatus } from '@azure/msal-browser'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'

const useMSAL = () => {
  const [msalInstance, setMsalInstance] = useState(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState(null)
  const [isInteractionInProgress, setIsInteractionInProgress] = useState(false)
  const interactionInProgressRef = useRef(false)

  useEffect(() => {
    const initializeMSAL = async () => {
      try {
        // MSAL Configuration
        const msalConfig = {
          auth: {
            clientId: process.env.REACT_APP_MICROSOFT_CLIENT_ID || 'your-microsoft-client-id',
            authority: process.env.REACT_APP_MICROSOFT_TENANT_ID 
              ? `https://login.microsoftonline.com/${process.env.REACT_APP_MICROSOFT_TENANT_ID}`
              : 'https://login.microsoftonline.com/common',
            redirectUri: window.location.origin + '/redirect'
          },
          cache: {
            cacheLocation: 'localStorage',
            storeAuthStateInCookie: false
          }
        }

        microsoftOAuthDebugger.debugMSALConfig(msalConfig)
        microsoftOAuthDebugger.info('Creating MSAL instance')
        
        const instance = new PublicClientApplication(msalConfig)
        setMsalInstance(instance)
        
        microsoftOAuthDebugger.info('Initializing MSAL instance')
        await instance.initialize()
        
        microsoftOAuthDebugger.success('MSAL instance initialized successfully')
        setIsInitialized(true)
        setError(null)
      } catch (err) {
        microsoftOAuthDebugger.error('Failed to initialize MSAL', {
          error: err.message,
          errorType: err.constructor.name
        })
        setError(err)
        setIsInitialized(false)
      }
    }

    initializeMSAL()
  }, [])

  // Monitor interaction status
  useEffect(() => {
    if (!msalInstance || !isInitialized) return

    const checkInteractionStatus = () => {
      const status = msalInstance.getActiveAccount()
      const inProgress = msalInstance.getInteractionStatus() === InteractionStatus.Interaction
      setIsInteractionInProgress(inProgress)
      interactionInProgressRef.current = inProgress
    }

    checkInteractionStatus()
    const interval = setInterval(checkInteractionStatus, 1000)
    return () => clearInterval(interval)
  }, [msalInstance, isInitialized])

  const login = async (scopes = ['User.Read']) => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }
    if (isInteractionInProgress || interactionInProgressRef.current) {
      microsoftOAuthDebugger.warning('Interaction already in progress, loginRedirect blocked')
      throw new Error('Đang có phiên đăng nhập Microsoft khác đang diễn ra. Vui lòng chờ...')
    }
    try {
      const loginRequest = { scopes }
      microsoftOAuthDebugger.info('Initiating Microsoft login redirect', loginRequest)
      setIsInteractionInProgress(true)
      interactionInProgressRef.current = true
      await msalInstance.loginRedirect(loginRequest)
      microsoftOAuthDebugger.success('MSAL login redirect initiated successfully')
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
    } catch (err) {
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
      microsoftOAuthDebugger.error('Error during Microsoft login initiation', {
        error: err.message,
        errorType: err.constructor.name
      })
      throw err
    }
  }

  const handleRedirectResult = async () => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }
    try {
      microsoftOAuthDebugger.info('Handling redirect result')
      const result = await msalInstance.handleRedirectPromise()
      
      if (result) {
        microsoftOAuthDebugger.success('Redirect result handled successfully', {
          account: result.account?.username,
          hasAccessToken: !!result.accessToken
        })
        return result
      } else {
        microsoftOAuthDebugger.info('No redirect result found')
        return null
      }
    } catch (err) {
      microsoftOAuthDebugger.error('Error handling redirect result', {
        error: err.message,
        errorType: err.constructor.name
      })
      throw err
    }
  }

  const getAccessToken = async (scopes = ['User.Read']) => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }
    try {
      const accounts = msalInstance.getAllAccounts()
      if (accounts.length === 0) {
        throw new Error('No accounts found')
      }

      const request = {
        scopes,
        account: accounts[0]
      }

      microsoftOAuthDebugger.info('Acquiring access token silently', request)
      const result = await msalInstance.acquireTokenSilent(request)
      microsoftOAuthDebugger.success('Access token acquired successfully')
      return result.accessToken
    } catch (err) {
      microsoftOAuthDebugger.error('Error acquiring access token', {
        error: err.message,
        errorType: err.constructor.name
      })
      throw err
    }
  }

  const logout = async () => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }
    try {
      microsoftOAuthDebugger.info('Logging out from Microsoft')
      await msalInstance.logoutRedirect()
      microsoftOAuthDebugger.success('Logout initiated successfully')
    } catch (err) {
      microsoftOAuthDebugger.error('Error during logout', {
        error: err.message,
        errorType: err.constructor.name
      })
      throw err
    }
  }

  const getActiveAccount = () => {
    if (!isInitialized || !msalInstance) {
      return null
    }
    return msalInstance.getActiveAccount()
  }

  return {
    msalInstance,
    isInitialized,
    error,
    isInteractionInProgress,
    login,
    logout,
    handleRedirectResult,
    getAccessToken,
    getActiveAccount
  }
}

export default useMSAL
