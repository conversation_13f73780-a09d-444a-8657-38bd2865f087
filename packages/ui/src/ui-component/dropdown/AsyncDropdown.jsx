import { Fragment, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import PropTypes from 'prop-types'
import axios from 'axios'
import ReactMarkdown from 'react-markdown'

// Material
import Autocomplete, { autocompleteClasses } from '@mui/material/Autocomplete'
import { Box, Button, Card, CardActions, Chip, CircularProgress, Popper, TextField, Typography } from '@mui/material'
import { styled } from '@mui/material/styles'
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import FormControlLabel from '@mui/material/FormControlLabel'
import Checkbox from '@mui/material/Checkbox'

// API
import credentialsApi from '@/api/credentials'

// const
import { baseURL } from '@/store/constant'
import CardContent from '@mui/material/CardContent'
import { IconCopy, IconEdit, IconFunction } from '@tabler/icons-react'

const StyledPopper = styled(Popper)({
  boxShadow: '0px 8px 10px -5px rgb(0 0 0 / 20%), 0px 16px 24px 2px rgb(0 0 0 / 14%), 0px 6px 30px 5px rgb(0 0 0 / 12%)',
  borderRadius: '10px',
  [`& .${autocompleteClasses.listbox}`]: {
    boxSizing: 'border-box',
    '& ul': {
      padding: 10,
      margin: 10
    }
  }
})

const fetchList = async ({ name, nodeData }) => {
  const loadMethod = nodeData.inputParams.find((param) => param.name === name)?.loadMethod
  const username = localStorage.getItem('username')
  const password = localStorage.getItem('password')

  return await axios
    .post(
      `${baseURL}/api/v1/node-load-method/${nodeData.name}`,
      { ...nodeData, loadMethod },
      {
        auth: username && password ? { username, password } : undefined,
        headers: { 'Content-type': 'application/json', 'x-request-from': 'internal' }
      }
    )
    .then(async function (response) {
      return response.data
    })
    .catch(function (error) {
      console.error(error)
    })
}

export const AsyncDropdown = ({
  name,
  nodeData,
  value,
  onSelect,
  isCreateNewOption,
  onCreateNew,
  credentialNames = [],
  disabled = false,
  disableClearable = false,
  multiple = false,
  label = ''
}) => {
  const customization = useSelector((state) => state.customization)

  const [open, setOpen] = useState(false)
  const [options, setOptions] = useState([])
  const [loading, setLoading] = useState(false)
  const [openDialog, setOpenDialog] = useState(false)
  const [selectedOptions, setSelectedOptions] = useState([])
  const findMatchingOptions = (options = [], value) => {
    if (multiple) {
      let values = []
      if ('choose an option' !== value && value && typeof value === 'string') {
        values = JSON.parse(value)
      } else {
        values = value
      }
      return options.filter((option) => values.includes(option.name))
    }

    return options.find((option) => option.name === value)
  }
  const getDefaultOptionValue = () => (multiple ? [] : '')
  const addNewOption = [{ label: '- Create New -', name: '-create-' }]
  let [internalValue, setInternalValue] = useState(value ?? 'choose an option')
  const [loadedValue, setLoadedValue] = useState(false)

  const fetchCredentialList = async () => {
    try {
      let names = ''
      if (credentialNames.length > 1) {
        names = credentialNames.join('&credentialName=')
      } else {
        names = credentialNames[0]
      }
      const resp = await credentialsApi.getCredentialsByName(names)
      if (resp.data) {
        const returnList = []
        for (let i = 0; i < resp.data.length; i += 1) {
          const data = {
            label: resp.data[i].name,
            name: resp.data[i].id
          }
          returnList.push(data)
        }
        return returnList
      }
    } catch (error) {
      console.error(error)
    }
  }

  const refreshList = async () => {
    setLoading(true)
    await (async () => {
      const fetchData = async () => {
        let response = credentialNames.length ? await fetchCredentialList() : await fetchList({ name, nodeData })
        if (isCreateNewOption) setOptions([...response, ...addNewOption])
        else setOptions([...response])

        const apiOptions = [...response]

        if (!loadedValue && multiple && apiOptions.length) {
          const newValues = findMatchingOptions(apiOptions, internalValue)
          setSelectedOptions(newValues)
          setLoadedValue(true)
        }
        setLoading(false)
      }
      void fetchData()
    })()
  }

  useEffect(() => {
    void refreshList()

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleOpenDialog = () => {
    setOpenDialog(true)

    void refreshList()
  }

  const handleCloseDialog = () => {
    setOpenDialog(false)
  }

  const handleOptionToggle = (option) => {
    const currentIndex = selectedOptions.findIndex((item) => item.name === option.name)
    const newSelectedOptions = [...selectedOptions]

    if (currentIndex === -1) {
      newSelectedOptions.push(option)
    } else {
      newSelectedOptions.splice(currentIndex, 1)
    }

    setSelectedOptions(newSelectedOptions)
  }

  const handleSaveOptions = () => {
    const selectedNames = selectedOptions.map((option) => option.name)
    const value = JSON.stringify(selectedNames)
    setInternalValue(value)
    onSelect(value)
    handleCloseDialog()
  }

  if (multiple) {
    return (
      <>
        <Card variant={'outlined'} className={'w-full mt-3'}>
          <CardContent className={'p-1'}>
            {loading && !options.length ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress size={24} />
              </Box>
            ) : selectedOptions.length > 0 ? (
              <Box>
                {[...selectedOptions].map((option) => (
                  <div key={option.name} className='relative group py-0.5 px-1 my-0.5 rounded bg-black/[0.04] flex items-center'>
                    <Box component='span' sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                      <IconFunction size={16} />
                    </Box>
                    <Box component='span'>{option.label}</Box>
                    <button
                      type='button'
                      onClick={() => navigator.clipboard.writeText(option.label)}
                      className='ml-auto p-1 opacity-0 group-hover:opacity-100'
                      title='Copy'
                    >
                      <IconCopy size={16} />
                    </button>
                  </div>
                ))}
              </Box>
            ) : (
              <Typography variant='body2' color='textSecondary' sx={{ p: 1, py: 0 }}>
                No options selected
              </Typography>
            )}
          </CardContent>
          <CardActions className={'p-1'}>
            <Button size='small' onClick={handleOpenDialog} title='Edit' startIcon={<IconEdit />}>
              Edit
            </Button>
          </CardActions>
        </Card>

        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth='sm' fullWidth>
          <DialogTitle>{label || name}</DialogTitle>
          <DialogContent dividers>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Box sx={{ py: 1 }}>
                {options.map((option) => (
                  <FormControlLabel
                    key={option.name}
                    control={
                      <Checkbox
                        checked={selectedOptions.some((item) => item.name === option.name)}
                        onChange={() => handleOptionToggle(option)}
                      />
                    }
                    label={
                      <Box>
                        <Typography variant='body1'>{option.label}</Typography>
                        {option.description && (
                          <Box title={option.description}>
                            <ReactMarkdown>{option.description}</ReactMarkdown>
                          </Box>
                        )}
                        {option.schema && option.schema.properties && (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                            {Object.keys(option.schema.properties).map((prop) => (
                              <Chip
                                key={prop}
                                label={prop}
                                size='small'
                                clickable
                                onClick={(e) => {
                                  e.preventDefault()
                                  e.stopPropagation()
                                  void navigator.clipboard.writeText(prop)
                                }}
                                title='Copy property name'
                              />
                            ))}
                          </Box>
                        )}
                      </Box>
                    }
                    className={'flex flex-row items-center gap-3 mb-2'}
                  />
                ))}
              </Box>
            )}
          </DialogContent>
          <DialogActions className={'gap-1'}>
            <Button onClick={handleCloseDialog}>Hủy bỏ</Button>
            <Button onClick={handleSaveOptions} variant='contained' color='primary'>
              Lưu
            </Button>
          </DialogActions>
        </Dialog>
      </>
    )
  }

  return (
    <>
      <Autocomplete
        id={name}
        disabled={disabled}
        disableClearable={disableClearable}
        multiple={multiple}
        filterSelectedOptions={multiple}
        size='small'
        sx={{ mt: 1, width: '100%' }}
        open={open}
        onOpen={() => {
          setOpen(true)
        }}
        onClose={() => {
          setOpen(false)
        }}
        options={options}
        value={findMatchingOptions(options, internalValue) || getDefaultOptionValue()}
        onChange={(e, selection) => {
          if (multiple) {
            let value = ''
            if (selection.length) {
              const selectionNames = selection.map((item) => item.name)
              value = JSON.stringify(selectionNames)
            }
            setInternalValue(value)
            onSelect(value)
          } else {
            const value = selection ? selection.name : ''
            if (isCreateNewOption && value === '-create-') {
              onCreateNew()
            } else {
              setInternalValue(value)
              onSelect(value)
            }
          }
        }}
        PopperComponent={StyledPopper}
        loading={loading}
        renderInput={(params) => (
          <TextField
            {...params}
            value={internalValue}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <Fragment>
                  {loading ? <CircularProgress color='inherit' size={20} /> : null}
                  {params.InputProps.endAdornment}
                </Fragment>
              )
            }}
            sx={{ height: '100%', '& .MuiInputBase-root': { height: '100%' } }}
          />
        )}
        renderOption={(props, option) => (
          <Box component='li' {...props}>
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <Typography variant='h5'>{option.label}</Typography>
              {option.description && (
                <Typography sx={{ color: customization.isDarkMode ? '#9e9e9e' : '' }}>{option.description}</Typography>
              )}
            </div>
          </Box>
        )}
      />
    </>
  )
}

AsyncDropdown.propTypes = {
  name: PropTypes.string,
  nodeData: PropTypes.object,
  value: PropTypes.string,
  onSelect: PropTypes.func,
  onCreateNew: PropTypes.func,
  disabled: PropTypes.bool,
  credentialNames: PropTypes.array,
  disableClearable: PropTypes.bool,
  isCreateNewOption: PropTypes.bool,
  multiple: PropTypes.bool,
  label: PropTypes.string,
  onRefresh: PropTypes.func
}
