import { useEffect } from 'react';
import useMSAL from '@/hooks/useMSAL';
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug';

const MicrosoftRedirectHandler = () => {
  const { handleRedirectPromise } = useMSAL();

  useEffect(() => {
    microsoftOAuthDebugger.info('MicrosoftRedirectHandler mounted, handling redirect promise');
    handleRedirectPromise()
      .then((response) => {
        microsoftOAuthDebugger.success('handleRedirectPromise success', response);
        // N<PERSON><PERSON> có accessToken, chuyển hướng về trang chính
        window.location.href = '/';
      })
      .catch((err) => {
        microsoftOAuthDebugger.error('handleRedirectPromise error', err);
        // Nếu lỗi, chuyển về trang login
        window.location.href = '/login';
      });
  }, [handleRedirectPromise]);

  return <div><PERSON>ang xử lý đăng nhập Microsoft...</div>;
};

export default MicrosoftRedirectHandler; 