import React, { useState } from 'react'
import { Box, Button, <PERSON>pography, <PERSON>lapse, Alert } from '@mui/material'
import useMSAL from '@/hooks/useMSAL'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'

const MicrosoftAuthDebugPanel = () => {
  const [isOpen, setIsOpen] = useState(false)
  const { msalInstance, isInitialized, isInteractionInProgress, clearCache, getAccounts } = useMSAL()

  const clearAllCache = () => {
    try {
      if (msalInstance) {
        // Use the clearCache method from useMSAL
        clearCache()

        // Clear localStorage items related to MSAL
        Object.keys(localStorage).forEach(key => {
          if (key.includes('msal') || key.includes('microsoft')) {
            localStorage.removeItem(key)
          }
        })

        // Clear sessionStorage items related to MSAL
        Object.keys(sessionStorage).forEach(key => {
          if (key.includes('msal') || key.includes('microsoft')) {
            sessionStorage.removeItem(key)
          }
        })

        microsoftOAuthDebugger.success('All Microsoft OAuth cache cleared')
        alert('Microsoft OAuth cache cleared successfully!')
      }
    } catch (error) {
      microsoftOAuthDebugger.error('Error clearing cache', error)
      alert('Error clearing cache: ' + error.message)
    }
  }

  const exportDebugLogs = () => {
    microsoftOAuthDebugger.exportLogs()
  }

  const clearDebugLogs = () => {
    microsoftOAuthDebugger.clearLogs()
    alert('Debug logs cleared!')
  }

  // Only show in development or when debug mode is enabled
  if (process.env.NODE_ENV === 'production' && localStorage.getItem('microsoft-oauth-debug') !== 'true') {
    return null
  }

  return (
    <Box sx={{ mt: 2 }}>
      <Button
        variant="text"
        size="small"
        onClick={() => setIsOpen(!isOpen)}
        sx={{ color: 'text.secondary', fontSize: '0.75rem' }}
      >
        {isOpen ? 'Hide' : 'Show'} Microsoft OAuth Debug
      </Button>
      
      <Collapse in={isOpen}>
        <Box sx={{ mt: 1, p: 2, border: '1px solid #e0e0e0', borderRadius: 1, backgroundColor: '#f9f9f9' }}>
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
            Microsoft OAuth Debug Panel
          </Typography>
          
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              <strong>Status:</strong>
            </Typography>
            <Alert severity={isInitialized ? 'success' : 'warning'} sx={{ mb: 1 }}>
              MSAL Initialized: {isInitialized ? 'Yes' : 'No'}
            </Alert>
            <Alert severity={isInteractionInProgress ? 'warning' : 'success'} sx={{ mb: 1 }}>
              Interaction In Progress: {isInteractionInProgress ? 'Yes' : 'No'}
            </Alert>
            <Alert severity="info">
              Accounts: {getAccounts().length} found
            </Alert>
          </Box>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>

            
            <Button
              variant="outlined"
              size="small"
              onClick={clearAllCache}
              disabled={!isInitialized}
              color="warning"
            >
              Clear All MSAL Cache
            </Button>
            
            <Button
              variant="outlined"
              size="small"
              onClick={exportDebugLogs}
            >
              Export Debug Logs
            </Button>
            
            <Button
              variant="outlined"
              size="small"
              onClick={clearDebugLogs}
            >
              Clear Debug Logs
            </Button>
          </Box>

          <Typography variant="caption" sx={{ mt: 2, display: 'block', color: 'text.secondary' }}>
            Use these tools to troubleshoot Microsoft OAuth issues. Clear cache if you're experiencing stuck login states.
          </Typography>
        </Box>
      </Collapse>
    </Box>
  )
}

export default MicrosoftAuthDebugPanel
