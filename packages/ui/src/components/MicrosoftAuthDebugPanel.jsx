import React, { useState, useEffect } from 'react'
import { 
  Box, 
  Typography, 
  Button, 
  Accordion, 
  AccordionSummary, 
  AccordionDetails,
  Chip,
  Alert
} from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import microsoftAuthStorage from '@/utils/microsoftAuthStorage'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'

const MicrosoftAuthDebugPanel = () => {
  const [authData, setAuthData] = useState({})
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Chỉ hiển thị khi debug mode được bật
    const debugMode = localStorage.getItem('microsoft-oauth-debug') === 'true'
    setIsVisible(debugMode)
    
    if (debugMode) {
      refreshData()
    }
  }, [])

  const refreshData = () => {
    const data = microsoftAuthStorage.getAllData()
    setAuthData(data)
  }

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'N/A'
    return new Date(timestamp).toLocaleString('vi-VN')
  }

  const formatTokenPreview = (token) => {
    if (!token) return 'N/A'
    return `${token.substring(0, 20)}...`
  }

  const getStatusChip = (hasData, label) => {
    return (
      <Chip 
        label={hasData ? `${label}: Có` : `${label}: Không`}
        color={hasData ? 'success' : 'default'}
        size="small"
        sx={{ mr: 1, mb: 1 }}
      />
    )
  }

  if (!isVisible) {
    return (
      <Box sx={{ position: 'fixed', bottom: 20, right: 20, zIndex: 1000 }}>
        <Button
          variant="outlined"
          size="small"
          onClick={() => {
            localStorage.setItem('microsoft-oauth-debug', 'true')
            setIsVisible(true)
            refreshData()
          }}
        >
          🔍 Debug Microsoft OAuth
        </Button>
      </Box>
    )
  }

  return (
    <Box 
      sx={{ 
        position: 'fixed', 
        bottom: 20, 
        right: 20, 
        width: 400, 
        maxHeight: '70vh',
        overflow: 'auto',
        backgroundColor: 'white',
        border: '1px solid #ddd',
        borderRadius: 2,
        boxShadow: 3,
        zIndex: 1000,
        p: 2
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">🔍 Microsoft OAuth Debug</Typography>
        <Button 
          size="small" 
          onClick={() => setIsVisible(false)}
          sx={{ minWidth: 'auto', p: 0.5 }}
        >
          ✕
        </Button>
      </Box>

      {/* Status Overview */}
      <Box sx={{ mb: 2 }}>
        {getStatusChip(authData.AUTH_CODE, 'Auth Code')}
        {getStatusChip(authData.ACCESS_TOKEN, 'Access Token')}
        {getStatusChip(authData.REFRESH_TOKEN, 'Refresh Token')}
        {getStatusChip(authData.AUTH_ERROR, 'Error')}
      </Box>

      {/* Action Buttons */}
      <Box sx={{ mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        <Button size="small" variant="outlined" onClick={refreshData}>
          🔄 Refresh
        </Button>
        <Button 
          size="small" 
          variant="outlined" 
          onClick={() => {
            microsoftAuthStorage.clearAll()
            refreshData()
          }}
        >
          🗑️ Clear All
        </Button>
        <Button 
          size="small" 
          variant="outlined" 
          onClick={() => {
            const logs = microsoftOAuthDebugger.getLogs()
            console.log('🔐 Microsoft OAuth Debug Logs:', logs)
            alert('Debug logs đã được in ra console')
          }}
        >
          📋 Show Logs
        </Button>
      </Box>

      {/* Authorization Code */}
      {authData.AUTH_CODE && (
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle2">Authorization Code</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="caption" display="block">
              <strong>Code:</strong> {formatTokenPreview(authData.AUTH_CODE.code)}
            </Typography>
            <Typography variant="caption" display="block">
              <strong>State:</strong> {authData.AUTH_CODE.state || 'N/A'}
            </Typography>
            <Typography variant="caption" display="block">
              <strong>Timestamp:</strong> {formatTimestamp(authData.AUTH_CODE.timestamp)}
            </Typography>
            <Typography variant="caption" display="block">
              <strong>URL:</strong> {authData.AUTH_CODE.url}
            </Typography>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Access Token */}
      {authData.ACCESS_TOKEN && (
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle2">Access Token</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="caption" display="block">
              <strong>Token:</strong> {formatTokenPreview(authData.ACCESS_TOKEN.accessToken)}
            </Typography>
            <Typography variant="caption" display="block">
              <strong>Account:</strong> {authData.ACCESS_TOKEN.account || 'N/A'}
            </Typography>
            <Typography variant="caption" display="block">
              <strong>Expires At:</strong> {formatTimestamp(authData.ACCESS_TOKEN.expiresAt)}
            </Typography>
            <Typography variant="caption" display="block">
              <strong>Scopes:</strong> {authData.ACCESS_TOKEN.scopes?.join(', ') || 'N/A'}
            </Typography>
          </AccordionDetails>
        </Accordion>
      )}

      {/* OAuth Error */}
      {authData.AUTH_ERROR && (
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle2" color="error">OAuth Error</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Alert severity="error" sx={{ mb: 1 }}>
              <Typography variant="caption" display="block">
                <strong>Error:</strong> {authData.AUTH_ERROR.error}
              </Typography>
              <Typography variant="caption" display="block">
                <strong>Description:</strong> {authData.AUTH_ERROR.errorDescription || 'N/A'}
              </Typography>
              <Typography variant="caption" display="block">
                <strong>Timestamp:</strong> {formatTimestamp(authData.AUTH_ERROR.timestamp)}
              </Typography>
            </Alert>
          </AccordionDetails>
        </Accordion>
      )}

      {/* URL Parameters */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Current URL Info</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="caption" display="block">
            <strong>URL:</strong> {window.location.href}
          </Typography>
          <Typography variant="caption" display="block">
            <strong>Search:</strong> {window.location.search || 'N/A'}
          </Typography>
          <Typography variant="caption" display="block">
            <strong>Hash:</strong> {window.location.hash || 'N/A'}
          </Typography>
        </AccordionDetails>
      </Accordion>
    </Box>
  )
}

export default MicrosoftAuthDebugPanel
