import React, { useState, useEffect } from 'react'
import {
  Box,
  Paper,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  Chip,
  IconButton,
  Collapse,
  Divider
} from '@mui/material'
import {
  BugReport,
  Download,
  Clear,
  ExpandMore,
  ExpandLess,
  Info,
  CheckCircle,
  Warning,
  Error,
  Code
} from '@mui/icons-material'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'

const MicrosoftOAuthDebugPanel = () => {
  const [logs, setLogs] = useState([])
  const [isExpanded, setIsExpanded] = useState(false)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV === 'development') {
      setIsVisible(true)
    }

    const updateLogs = () => {
      setLogs([...microsoftOAuthDebugger.getLogs()])
    }

    // Update logs every second
    const interval = setInterval(updateLogs, 1000)
    updateLogs()

    return () => clearInterval(interval)
  }, [])

  const getLogIcon = (level) => {
    switch (level) {
      case 'info':
        return <Info color="primary" fontSize="small" />
      case 'success':
        return <CheckCircle color="success" fontSize="small" />
      case 'warning':
        return <Warning color="warning" fontSize="small" />
      case 'error':
        return <Error color="error" fontSize="small" />
      case 'debug':
        return <Code color="action" fontSize="small" />
      default:
        return <Info color="primary" fontSize="small" />
    }
  }

  const getLogColor = (level) => {
    switch (level) {
      case 'info':
        return 'primary'
      case 'success':
        return 'success'
      case 'warning':
        return 'warning'
      case 'error':
        return 'error'
      case 'debug':
        return 'default'
      default:
        return 'primary'
    }
  }

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString()
  }

  const handleExportLogs = () => {
    microsoftOAuthDebugger.exportLogs()
  }

  const handleClearLogs = () => {
    microsoftOAuthDebugger.clearLogs()
    setLogs([])
  }

  const handleToggleDebug = () => {
    const newState = !microsoftOAuthDebugger.isEnabled
    microsoftOAuthDebugger.setEnabled(newState)
  }

  if (!isVisible) return null

  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: 16,
        right: 16,
        zIndex: 9999,
        maxWidth: 600,
        width: '100%'
      }}
    >
      <Paper
        elevation={8}
        sx={{
          maxHeight: isExpanded ? 400 : 60,
          overflow: 'hidden',
          transition: 'max-height 0.3s ease-in-out'
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            p: 1,
            bgcolor: 'primary.main',
            color: 'white',
            cursor: 'pointer'
          }}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <BugReport />
            <Typography variant="subtitle2">
              Microsoft OAuth Debug ({logs.length} logs)
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip
              label={microsoftOAuthDebugger.isEnabled ? 'ON' : 'OFF'}
              size="small"
              color={microsoftOAuthDebugger.isEnabled ? 'success' : 'default'}
              onClick={(e) => {
                e.stopPropagation()
                handleToggleDebug()
              }}
            />
            {isExpanded ? <ExpandLess /> : <ExpandMore />}
          </Box>
        </Box>

        {/* Content */}
        <Collapse in={isExpanded}>
          <Box sx={{ p: 1 }}>
            {/* Actions */}
            <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
              <Button
                size="small"
                startIcon={<Download />}
                onClick={handleExportLogs}
                variant="outlined"
              >
                Export
              </Button>
              <Button
                size="small"
                startIcon={<Clear />}
                onClick={handleClearLogs}
                variant="outlined"
                color="error"
              >
                Clear
              </Button>
            </Box>

            <Divider sx={{ mb: 1 }} />

            {/* Logs */}
            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
              {logs.length === 0 ? (
                <Typography variant="body2" color="textSecondary" sx={{ p: 2, textAlign: 'center' }}>
                  No logs yet. Try logging in with Microsoft to see debug information.
                </Typography>
              ) : (
                <List dense>
                  {logs.slice(-20).reverse().map((log, index) => (
                    <ListItem key={index} sx={{ py: 0.5 }}>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {getLogIcon(log.level)}
                            <Typography variant="body2" component="span">
                              {log.message}
                            </Typography>
                            <Chip
                              label={log.level}
                              size="small"
                              color={getLogColor(log.level)}
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                            <Typography variant="caption" color="textSecondary">
                              {formatTimestamp(log.timestamp)}
                            </Typography>
                            {log.data && (
                              <Typography variant="caption" color="textSecondary">
                                {typeof log.data === 'object' ? JSON.stringify(log.data) : log.data}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </Box>
          </Box>
        </Collapse>
      </Paper>
    </Box>
  )
}

export default MicrosoftOAuthDebugPanel 