import React, { useState } from 'react'
import PropTypes from 'prop-types'
import { Box, Typography, IconButton, Tooltip, Collapse } from '@mui/material'
import { IconLanguage, IconLanguageOff } from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

const PhoneticDisplay = ({ originalText, phoneticText, language, showPhonetic = true, onTogglePhonetic, isInputPhonetic = false }) => {
  const { t } = useTranslation()
  const [isExpanded, setIsExpanded] = useState(showPhonetic)

  const handleToggle = () => {
    // Don't allow toggle for input phonetic (read-only)
    if (isInputPhonetic) return

    const newState = !isExpanded
    setIsExpanded(newState)
    if (onTogglePhonetic) {
      onTogglePhonetic(newState)
    }
  }

  // const getLanguageLabel = (lang) => {
  //   switch (lang) {
  //     case t('languages.chinese'):
  //       return t('phonetic.pinyin')
  //     case t('languages.japanese'):
  //       return t('phonetic.romaji')
  //     default:
  //       return t('phonetic.phonetic')
  //   }
  // }

  const getPhoneticStyle = (lang) => {
    switch (lang) {
      case t('languages.chinese'):
        return {
          fontFamily: '"Google Sans", "Roboto", "Microsoft YaHei", "PingFang SC", sans-serif',
          fontSize: '14px',
          color: '#5f6368',
          fontWeight: 400,
          lineHeight: '20px'
        }
      case t('languages.japanese'):
        return {
          fontFamily: '"Google Sans", "Roboto", "Hiragino Sans", "Yu Gothic", sans-serif',
          fontSize: '14px',
          color: '#5f6368',
          fontWeight: 400,
          fontStyle: 'italic',
          lineHeight: '20px'
        }
      default:
        return {
          fontFamily: '"Google Sans", "Roboto", Arial, sans-serif',
          fontSize: '14px',
          color: '#5f6368',
          fontWeight: 400,
          lineHeight: '20px'
        }
    }
  }

  if (!phoneticText || phoneticText.trim().length === 0) {
    // For input phonetic, don't show anything if no phonetic text
    if (isInputPhonetic) {
      return null
    }

    return (
      <Typography
        variant='body1'
        sx={{
          fontFamily: '"Google Sans", "Roboto", Arial, sans-serif',
          fontSize: '16px',
          lineHeight: '24px',
          color: '#3c4043',
          mb: 2,
          whiteSpace: 'pre-wrap',
          opacity: !originalText ? 0.4 : 1
        }}
      >
        {originalText || t('translation.translationWillAppear')}
      </Typography>
    )
  }

  return (
    <Box sx={{ mb: isInputPhonetic ? 1 : 2 }}>
      {/* Main text - hide for input phonetic since it's already shown in the input field */}
      {!isInputPhonetic && (
        <Typography
          variant='body1'
          sx={{
            fontFamily: '"Google Sans", "Roboto", Arial, sans-serif',
            fontSize: '16px',
            lineHeight: '24px',
            color: '#3c4043',
            mb: 1,
            whiteSpace: 'pre-wrap'
          }}
        >
          {originalText}
        </Typography>
      )}

      {/* Phonetic transcription section */}
      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
        {/* Hide toggle button for input phonetic */}
        {!isInputPhonetic && (
          <Tooltip
          // title={
          //   isExpanded
          //     ? t('phonetic.hide', { type: getLanguageLabel(language) })
          //     : t('phonetic.show', { type: getLanguageLabel(language) })
          // }
          >
            <IconButton
              size='small'
              onClick={handleToggle}
              sx={{
                color: isExpanded ? '#1a73e8' : '#9aa0a6',
                '&:hover': {
                  backgroundColor: 'rgba(26, 115, 232, 0.1)',
                  color: '#1a73e8'
                },
                mt: 0.5,
                transition: 'all 0.2s ease'
              }}
            >
              {isExpanded ? <IconLanguageOff size={18} /> : <IconLanguage size={18} />}
            </IconButton>
          </Tooltip>
        )}

        <Box sx={{ flex: 1, ml: isInputPhonetic ? 0 : undefined }}>
          <Collapse in={isExpanded}>
            <Box
              sx={{
                backgroundColor: '#f8f9fa',
                borderRadius: '8px',
                padding: isInputPhonetic ? '8px 12px' : '12px 16px',
                border: '1px solid #e8eaed',
                boxShadow: '0 1px 2px rgba(0,0,0,0.05)'
              }}
            >
              {/* <Typography
                variant='caption'
                sx={{
                  color: '#5f6368',
                  fontSize: '12px',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px',
                  display: 'block',
                  mb: 0.5
                }}
              >
                {getLanguageLabel(language)}
              </Typography> */}
              <Typography
                sx={{
                  ...getPhoneticStyle(language),
                  lineHeight: '20px',
                  wordBreak: 'break-word',
                  fontSize: isInputPhonetic ? '14px' : '16px',
                  whiteSpace: 'pre-wrap' // Preserve line breaks and whitespace
                }}
              >
                {phoneticText}
              </Typography>
            </Box>
          </Collapse>
        </Box>
      </Box>
    </Box>
  )
}

PhoneticDisplay.propTypes = {
  originalText: PropTypes.string,
  phoneticText: PropTypes.string,
  language: PropTypes.string,
  showPhonetic: PropTypes.bool,
  onTogglePhonetic: PropTypes.func,
  isInputPhonetic: PropTypes.bool
}

export default PhoneticDisplay
