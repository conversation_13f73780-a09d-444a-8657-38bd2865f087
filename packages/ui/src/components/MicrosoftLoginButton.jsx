import React, { useState, useEffect } from 'react'
import { Button, CircularProgress } from '@mui/material'
import { InteractionRequiredAuthError } from '@azure/msal-browser'
import userApi from '@/api/user'
import { useDispatch } from 'react-redux'
import { logoutAction } from '@/store/actions'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'
import useMSAL from '@/hooks/useMSAL'

const MicrosoftLoginButton = ({ onError = () => {} }) => {
  const [isLoading, setIsLoading] = useState(false)
  const dispatch = useDispatch()
  const logout = (...args) => dispatch(logoutAction(...args))
  
  // Use MSAL hook
  const { isInitialized, error: msalError, login, handleRedirectPromise } = useMSAL()

  // MicrosoftLoginButton chỉ xử lý việc khởi tạo login, không xử lý callback
  // Callback sẽ được xử lý bởi MicrosoftRedirect component

  const handleMicrosoftLogin = async () => {
    if (!isInitialized) {
      microsoftOAuthDebugger.error('MSAL not initialized, cannot proceed with login')
      onError('Microsoft Authentication chưa sẵn sàng. Vui lòng thử lại sau.')
      return
    }

    if (msalError) {
      microsoftOAuthDebugger.error('MSAL has error, cannot proceed with login', msalError)
      onError('Microsoft Authentication có lỗi. Vui lòng thử lại sau.')
      return
    }

    microsoftOAuthDebugger.info('Microsoft login button clicked')
    setIsLoading(true)
    
    try {
      await login(['User.Read'])
    } catch (error) {
      microsoftOAuthDebugger.error('Error during Microsoft login initiation', {
        error: error.message,
        errorType: error.constructor.name
      })
      
      if (error instanceof InteractionRequiredAuthError) {
        microsoftOAuthDebugger.warning('User cancelled the login (InteractionRequiredAuthError)')
        setIsLoading(false)
      } else {
        microsoftOAuthDebugger.error('Unexpected error during login initiation')
        onError('Đã có lỗi xảy ra khi khởi tạo đăng nhập Microsoft.')
        setIsLoading(false)
      }
    }
  }

  microsoftOAuthDebugger.debug('Rendering Microsoft login button', { 
    isLoading, 
    isInitialized, 
    hasError: !!msalError 
  })

  return (
    <Button
      fullWidth
      variant="outlined"
      size="large"
      disabled={isLoading || !isInitialized}
      onClick={handleMicrosoftLogin}
      sx={{
        py: 1.5,
        fontSize: { xs: '0.875rem', sm: '1rem' },
        borderColor: '#0078d4',
        color: '#0078d4',
        backgroundColor: 'white',
        '&:hover': {
          backgroundColor: '#f3f2f1',
          borderColor: '#106ebe'
        },
        '&:disabled': {
          backgroundColor: '#f3f2f1',
          borderColor: '#c8c6c4',
          color: '#a19f9d'
        }
      }}
      startIcon={
        isLoading ? (
          <CircularProgress size={20} sx={{ color: '#0078d4' }} />
        ) : (
          <svg width="20" height="20" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill="#f25022" d="M1 1h9v9H1z"/>
            <path fill="#00a4ef" d="M11 1h9v9h-9z"/>
            <path fill="#7fba00" d="M1 11h9v9H1z"/>
            <path fill="#ffb900" d="M11 11h9v9h-9z"/>
          </svg>
        )
      }
    >
      {isLoading ? 'Đang xử lý...' : 'Đăng nhập với Microsoft'}
    </Button>
  )
}

export default MicrosoftLoginButton
