import React, { useState } from 'react'
import { Button, CircularProgress, Box } from '@mui/material'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'
import useMSAL from '@/hooks/useMSAL'

const MicrosoftLoginButton = ({ onError, disabled = false, fullWidth = true }) => {
  const [isLoading, setIsLoading] = useState(false)
  const { isInitialized, login, error: msalError } = useMSAL()

  const handleMicrosoftLogin = async () => {
    if (!isInitialized) {
      const errorMsg = 'Microsoft authentication is not ready. Please try again.'
      microsoftOAuthDebugger.error('MSAL not initialized when login attempted')
      onError?.(errorMsg)
      return
    }

    if (isLoading) {
      microsoftOAuthDebugger.warning('Login already in progress')
      return
    }

    setIsLoading(true)
    microsoftOAuthDebugger.info('Microsoft login button clicked')

    try {
      await login(['User.Read'])
      microsoftOAuthDebugger.success('Microsoft login redirect initiated')
      // The redirect will happen, so we don't need to do anything else here
    } catch (error) {
      microsoftOAuthDebugger.error('Microsoft login failed', {
        error: error.message,
        errorType: error.constructor.name
      })

      let errorMessage = 'Đăng nhập Microsoft thất bại. Vui lòng thử lại.'

      if (error.message.includes('Đang có phiên đăng nhập Microsoft khác')) {
        errorMessage = error.message
      } else if (error.message.includes('popup_window_error')) {
        errorMessage = 'Không thể mở cửa sổ đăng nhập Microsoft. Vui lòng kiểm tra trình chặn popup.'
      } else if (error.message.includes('network')) {
        errorMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.'
      }

      onError?.(errorMessage)
      setIsLoading(false)
    }
  }

  // Show error state if MSAL failed to initialize
  if (msalError) {
    return (
      <Button
        fullWidth={fullWidth}
        variant='outlined'
        disabled
        sx={{
          mt: 1,
          py: 1.5,
          borderColor: '#f44336',
          color: '#f44336',
          '&:hover': {
            borderColor: '#f44336',
            backgroundColor: 'rgba(244, 67, 54, 0.04)'
          }
        }}
      >
        Microsoft Login Unavailable
      </Button>
    )
  }

  return (
    <Button
      fullWidth={fullWidth}
      variant='outlined'
      onClick={handleMicrosoftLogin}
      disabled={disabled || !isInitialized || isLoading}
      sx={{
        mt: 1,
        py: 1.5,
        borderColor: '#0078d4',
        color: '#0078d4',
        backgroundColor: 'white',
        '&:hover': {
          borderColor: '#106ebe',
          backgroundColor: 'rgba(0, 120, 212, 0.04)',
          color: '#106ebe'
        },
        '&:disabled': {
          borderColor: '#e0e0e0',
          color: '#9e9e9e'
        }
      }}
      startIcon={
        isLoading ? (
          <CircularProgress size={20} sx={{ color: '#0078d4' }} />
        ) : (
          <Box
            component='img'
            src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMSIgeT0iMSIgd2lkdGg9IjkiIGhlaWdodD0iOSIgZmlsbD0iI0Y0NTIzNiIvPgo8cmVjdCB4PSIxMSIgeT0iMSIgd2lkdGg9IjkiIGhlaWdodD0iOSIgZmlsbD0iIzAwQkNGMiIvPgo8cmVjdCB4PSIxIiB5PSIxMSIgd2lkdGg9IjkiIGhlaWdodD0iOSIgZmlsbD0iI0ZGQjkwMCIvPgo8cmVjdCB4PSIxMSIgeT0iMTEiIHdpZHRoPSI5IiBoZWlnaHQ9IjkiIGZpbGw9IiMwMEE5NTIiLz4KPC9zdmc+'
            alt='Microsoft'
            sx={{ width: 20, height: 20 }}
          />
        )
      }
    >
      {isLoading ? 'Đang xử lý...' : 'Đăng nhập với Microsoft'}
    </Button>
  )
}

export default MicrosoftLoginButton
