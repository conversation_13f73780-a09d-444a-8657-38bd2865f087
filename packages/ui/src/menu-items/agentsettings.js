// assets
import {
  IconTrash,
  IconFileUpload,
  IconFileExport,
  IconCopy,
  IconMessage,
  IconDatabaseExport,
  IconAdjustmentsHorizontal,
  IconUsers,
  IconTemplate
} from '@tabler/icons-react'

// constant
const icons = {
  IconTrash,
  IconFileUpload,
  IconFileExport,
  IconCopy,
  IconMessage,
  IconDatabaseExport,
  IconAdjustmentsHorizontal,
  IconUsers,
  IconTemplate
}

// ==============================|| SETTINGS MENU ITEMS ||============================== //

const agent_settings = {
  id: 'settings',
  title: '',
  type: 'group',
  children: [
    {
      id: 'viewMessages',
      title: 'View Messages',
      type: 'item',
      url: '',
      icon: icons.IconMessage
    },
    {
      id: 'viewLeads',
      title: 'View Leads',
      type: 'item',
      url: '',
      icon: icons.IconUsers
    },
    {
      id: 'chatflowConfiguration',
      title: 'Configuration',
      type: 'item',
      url: '',
      icon: icons.IconAdjustmentsHorizontal
    },
    {
      id: 'saveAsTemplate',
      title: 'Save As Template',
      type: 'item',
      url: '',
      icon: icons.IconTemplate
    },
    {
      id: 'duplicateChatflow',
      title: 'Duplicate Agents',
      type: 'item',
      url: '',
      icon: icons.IconCopy
    },
    {
      id: 'loadChatflow',
      title: 'Load Agents',
      type: 'item',
      url: '',
      icon: icons.IconFileUpload
    },
    {
      id: 'exportChatflow',
      title: 'Export Agents',
      type: 'item',
      url: '',
      icon: icons.IconFileExport
    },
    {
      id: 'deleteChatflow',
      title: 'Delete Agents',
      type: 'item',
      url: '',
      icon: icons.IconTrash
    }
  ]
}

export default agent_settings
