import { lazy } from 'react'

import Loadable from '@/ui-component/loading/Loadable'

// project imports
import MinimalLayout from '@/layout/MinimalLayout'

const LoginAccount = Loadable(lazy(() => import('@/views/Login')))
const MicrosoftRedirect = Loadable(lazy(() => import('@/views/MicrosoftRedirect')))

// canvas routing

// ==============================|| CANVAS ROUTING ||============================== //

const LoginRoute = {
  path: '/',
  element: <MinimalLayout />,
  children: [
    {
      path: '/login',
      element: <LoginAccount />
    },
    {
      path: '/redirect',
      element: <MicrosoftRedirect />
    }
  ]
}

export default LoginRoute
