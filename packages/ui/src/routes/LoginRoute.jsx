import { lazy } from 'react'

import Loadable from '@/ui-component/loading/Loadable'

// project imports
import MinimalLayout from '@/layout/MinimalLayout'

const LoginAccount = Loadable(lazy(() => import('@/views/Login')))

// canvas routing

// ==============================|| CANVAS ROUTING ||============================== //

const LoginRoute = {
  path: '/',
  element: <MinimalLayout />,
  children: [
    {
      path: '/login',
      element: <LoginAccount />
    }
  ]
}

export default LoginRoute
