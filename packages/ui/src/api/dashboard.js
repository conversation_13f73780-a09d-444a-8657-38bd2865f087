import client from './client'

export const getChatSessionsHistory = (params) => client.get('/dashboard/chat-history', { params })

export const getQuestionUnanswered = (params) => client.get(`/faq/list-classify-qa/${params.flowId}`)

export const getCustomerFeedback = (params) => client.get('/dashboard/customer-feedback', { params })

export const getQuestionOverTime = (params) => client.get('/dashboard/questions-over-time', { params })

export const getQuestionAnswerByMessageId = (id) => client.get(`/feedback/message-id/${id}`)
