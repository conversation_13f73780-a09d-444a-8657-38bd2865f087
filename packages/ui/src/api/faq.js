import client from './client'

const getAllFaqs = async (chatflowId, limit = 20, offset = 0) => {
  try {
    const response = await client.get(`/faq`, {
      params: {
        chatflowId,
        limit,
        offset
      }
    })
    return response.data
  } catch (error) {
    throw new Error(`Failed to fetch FAQs: ${error.message}`)
  }
}
// const getFaqById = (id, chatflowId) => client.get(`/faq/${id}?chatflowId=${chatflowId}`)

const searchFaqs = (chatflowId) => client.get(`/faq/search?chatflowId=${chatflowId}`)

const saveFaq = (faq) => client.post('/faq', faq)

const importFaqs = (faqs) => client.post('/faq/importfaqs', faqs)

const updateFaq = (id, faq) => client.put(`/faq/${id}`, faq)

const deleteFaq = async (id, chatflowId) => client.delete(`/faq/delete/${id}/${chatflowId}`)

const deleteAllFaqs = (chatflowId) => client.delete(`/faq/deleteall/${chatflowId}`)

const deleteIndex = (chatflowId) => client.delete(`/faq/deleteindex/${chatflowId}`)

const updateSettings = (chatflowId) => client.post(`/faq/settings/${chatflowId}`)

const getListClassifyQA = (chatflowId) => client.get(`/faq/list-classify-qa/${chatflowId}`)

const createOrUpdateLabel = (body) => client.post(`/faq/label`, body)

const deleteLabel = (id, chatflowId) => client.delete(`/faq/label/${id}/${chatflowId}`)

export default {
  getAllFaqs,
  // getFaqById,
  searchFaqs,
  saveFaq,
  importFaqs,
  updateFaq,
  deleteFaq,
  deleteAllFaqs,
  deleteIndex,
  updateSettings,
  getListClassifyQA,
  createOrUpdateLabel,
  deleteLabel
}
