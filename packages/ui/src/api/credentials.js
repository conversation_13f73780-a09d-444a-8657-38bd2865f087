import client from './client'

const getAllCredentials = (userId = '') => client.get(`/credentials?userId=${userId}`)

const getCredentialsByName = (componentCredentialName) => client.get(`/credentials?credentialName=${componentCredentialName}`)

const getAllComponentsCredentials = () => client.get('/components-credentials')

const getSpecificCredential = (id) => client.get(`/credentials/${id}`)

const getSpecificComponentCredential = (name) => client.get(`/components-credentials/${name}`)

const createCredential = (body) => client.post(`/credentials`, body)

const updateCredential = (id, body) => client.put(`/credentials/${id}`, body)

const deleteCredential = (id) => client.delete(`/credentials/${id}`)

export default {
  getAllCredentials,
  getCredentialsByName,
  getAllComponentsCredentials,
  getSpecificCredential,
  getSpecificComponentCredential,
  createCredential,
  updateCredential,
  deleteCredential
}
