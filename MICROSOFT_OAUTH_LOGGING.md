# Microsoft OAuth2 Logging Guide

## Tổng quan

Hệ thống logging chi tiết đã được thêm vào Microsoft OAuth2 để giúp debug và theo dõi quá trình đăng nhập. Logging được thực hiện ở cả Backend và Frontend.

## Backend Logging

### Các log được ghi:

1. **Service Layer** (`packages/server/src/services/user/index.ts`):
   - Bắt đầu quá trình login Microsoft
   - Nhận access token từ Frontend
   - Gọi Microsoft Graph API
   - Thông tin user từ Microsoft
   - Kiểm tra user trong database
   - Tạo/cập nhật user
   - Tạo JWT tokens
   - Lỗi và exception

2. **Controller Layer** (`packages/server/src/controllers/user/index.ts`):
   - Nhận request từ Frontend
   - Headers của request
   - Validation access token
   - Gọi service
   - Response cho client

### Format log:
```
🔐 [Microsoft OAuth]: <message>
🔐 [Microsoft OAuth Controller]: <message>
```

### Ví dụ log:
```
🔐 [Microsoft OAuth]: Starting Microsoft login process
🔐 [Microsoft OAuth]: Access token received (length: 1234)
🔐 [Microsoft OAuth]: Fetching user info from Microsoft Graph API
🔐 [Microsoft OAuth]: Successfully retrieved user info from Microsoft Graph API
🔐 [Microsoft OAuth]: User info - ID: abc123, DisplayName: John Doe, Email: <EMAIL>
```

## Frontend Logging

### Các log được ghi:

1. **MicrosoftLoginButton Component**:
   - Khởi tạo MSAL configuration
   - Tạo MSAL instance
   - Kiểm tra OAuth callback
   - URL parameters
   - LocalStorage state
   - MSAL redirect response
   - API calls đến Backend
   - Lưu data và redirect

2. **API Client** (`packages/ui/src/api/client.js`):
   - Tất cả API requests
   - Microsoft OAuth specific requests
   - API responses
   - API errors

### Format log:
```
🔐 [Microsoft OAuth Frontend] [timestamp]: <message>
🔐 [API Client] [timestamp]: <message>
```

## Debug Utility

### MicrosoftOAuthDebugger

Một utility class đã được tạo để quản lý logging:

```javascript
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'

// Các methods có sẵn:
microsoftOAuthDebugger.info(message, data)
microsoftOAuthDebugger.success(message, data)
microsoftOAuthDebugger.warning(message, data)
microsoftOAuthDebugger.error(message, data)
microsoftOAuthDebugger.debug(message, data)

// Debug specific data:
microsoftOAuthDebugger.debugMSALConfig(config)
microsoftOAuthDebugger.debugURLParams()
microsoftOAuthDebugger.debugLocalStorage()
microsoftOAuthDebugger.debugNetworkRequest(config)
microsoftOAuthDebugger.debugNetworkResponse(response)
microsoftOAuthDebugger.debugNetworkError(error)

// Utility methods:
microsoftOAuthDebugger.getLogs()
microsoftOAuthDebugger.exportLogs()
microsoftOAuthDebugger.clearLogs()
microsoftOAuthDebugger.setEnabled(enabled)
```

## Debug Panel

Trong development mode, một debug panel sẽ xuất hiện ở góc dưới bên phải màn hình:

### Tính năng:
- Hiển thị real-time logs
- Export logs ra file JSON
- Clear logs
- Toggle debug mode on/off
- Expand/collapse panel

### Cách sử dụng:
1. Mở Developer Tools (F12)
2. Debug panel sẽ xuất hiện ở góc dưới phải
3. Click để expand/collapse
4. Sử dụng các buttons để quản lý logs

## Cách bật/tắt logging

### Backend:
Logging luôn được bật trong Backend và được ghi vào server logs.

### Frontend:
```javascript
// Bật debug mode
localStorage.setItem('microsoft-oauth-debug', 'true')

// Tắt debug mode
localStorage.setItem('microsoft-oauth-debug', 'false')

// Hoặc sử dụng debug panel
```

## Troubleshooting với logs

### 1. Lỗi "No email found in Microsoft user data":
```
🔐 [Microsoft OAuth]: No email found in Microsoft user data
```
**Giải pháp**: Kiểm tra Microsoft Graph API permissions, đảm bảo có quyền `User.Read`

### 2. Lỗi "Microsoft Graph API error":
```
🔐 [Microsoft OAuth]: Microsoft Graph API error - Status: 401
```
**Giải pháp**: Kiểm tra access token có hợp lệ không, scope có đúng không

### 3. Lỗi "Missing access token":
```
🔐 [Microsoft OAuth Controller]: Missing access token in request body
```
**Giải pháp**: Kiểm tra Frontend có gửi access token không

### 4. Lỗi "User account is inactive":
```
🔐 [Microsoft OAuth]: User account is inactive - ID: xxx
```
**Giải pháp**: Kích hoạt user account trong database

## Export logs

### Từ Debug Panel:
1. Click "Export" button
2. File JSON sẽ được download
3. File chứa tất cả logs và environment info

### Từ Console:
```javascript
// Export logs
window.microsoftOAuthDebugger.exportLogs()

// Xem logs trong console
console.log(window.microsoftOAuthDebugger.getLogs())
```

## Performance

- Logs được giới hạn 100 entries để tránh memory leak
- Debug panel chỉ hiển thị trong development mode
- Logging có thể được tắt để tăng performance

## Security

- Access tokens không được log đầy đủ (chỉ hiển thị 20 ký tự đầu)
- Sensitive data được mask trong logs
- Debug panel chỉ hiển thị trong development

## Monitoring

Để monitor logs trong production:

1. **Backend**: Sử dụng log aggregation tools (ELK, Splunk, etc.)
2. **Frontend**: Gửi logs đến monitoring service
3. **Database**: Theo dõi user login events

## Best Practices

1. **Development**: Sử dụng debug panel để debug
2. **Staging**: Bật logging để test
3. **Production**: Tắt debug panel, chỉ giữ essential logs
4. **Monitoring**: Set up alerts cho error logs
5. **Retention**: Xóa logs cũ định kỳ 