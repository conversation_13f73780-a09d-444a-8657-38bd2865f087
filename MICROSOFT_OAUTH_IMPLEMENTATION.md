# Microsoft OAuth2 Implementation Guide

## Overview

This document provides a comprehensive guide for the Microsoft OAuth2 authentication implementation in the Flowise application. The implementation supports both personal Microsoft accounts and business/organizational accounts (Microsoft 365/Azure AD).

## Features Implemented

✅ **Backend Implementation:**
- Microsoft OAuth service with token validation
- User creation/authentication logic with Microsoft Graph API integration
- Proper error handling and logging
- JWT token generation for authenticated users
- Database schema updates for Microsoft OAuth fields

✅ **Frontend Implementation:**
- MSAL (Microsoft Authentication Library) integration
- Microsoft login button component
- Dedicated redirect page for OAuth callback handling
- Comprehensive error handling and user feedback
- Debug logging utility for troubleshooting

✅ **Security & Configuration:**
- Environment variable configuration for sensitive data
- Proper token validation with Microsoft Graph API
- User account linking and creation logic
- Whitelist configuration for OAuth endpoints

## Architecture

### Backend Components

1. **Microsoft OAuth Service** (`packages/server/src/services/microsoftOAuth/index.ts`)
   - Handles MSAL configuration and initialization
   - Validates access tokens with Microsoft Graph API
   - Manages user creation and account linking
   - Generates JWT tokens for authenticated users

2. **Microsoft OAuth Controller** (`packages/server/src/controllers/microsoftOAuth/index.ts`)
   - Exposes REST API endpoint for Microsoft OAuth login
   - Handles request validation and response formatting
   - Integrates with the Microsoft OAuth service

3. **Database Schema Updates**
   - Added `microsoftId` field to User entity
   - Added `displayName` field to User entity
   - Migration script for database updates

### Frontend Components

1. **MSAL Hook** (`packages/ui/src/hooks/useMSAL.js`)
   - Manages MSAL instance initialization
   - Handles login redirects and callback processing
   - Provides authentication state management

2. **Microsoft Login Button** (`packages/ui/src/components/MicrosoftLoginButton.jsx`)
   - Renders Microsoft login button with proper styling
   - Integrates with MSAL hook for authentication flow
   - Handles loading states and error feedback

3. **Microsoft Redirect Handler** (`packages/ui/src/views/MicrosoftRedirect/index.jsx`)
   - Dedicated page for handling OAuth callback
   - Processes authentication response from Microsoft
   - Manages user session and navigation

4. **Debug Utility** (`packages/ui/src/utils/microsoftOAuthDebug.js`)
   - Comprehensive logging for troubleshooting
   - Debug mode toggle functionality
   - Log export capabilities

## Configuration

### Environment Variables

#### Backend (.env)
```env
# Microsoft OAuth2 Configuration
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret
MICROSOFT_TENANT_ID=your-tenant-id
```

#### Frontend (.env)
```env
# Microsoft OAuth2 Configuration
REACT_APP_MICROSOFT_CLIENT_ID=your-microsoft-client-id
REACT_APP_MICROSOFT_TENANT_ID=your-tenant-id
```

### Azure Portal Configuration

1. **App Registration:**
   - Navigate to Azure Portal > Azure Active Directory > App registrations
   - Create new registration with name "Flowise OAuth2"
   - Set supported account types to "Accounts in any organizational directory and personal Microsoft accounts"
   - Configure redirect URI: `http://localhost:8080/redirect` (development) or `https://yourdomain.com/redirect` (production)

2. **API Permissions:**
   - Add Microsoft Graph > Delegated permissions > User.Read
   - Grant admin consent for the permissions

3. **Client Secret:**
   - Generate client secret in "Certificates & secrets" section
   - Copy the secret value (only shown once)

## API Endpoints

### POST /api/v1/user/login/microsoft

Authenticates user with Microsoft OAuth access token.

**Request Body:**
```json
{
  "accessToken": "microsoft_access_token_here"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user_uuid",
    "username": "<EMAIL>",
    "email": "<EMAIL>",
    "displayName": "User Name",
    "role": "USER",
    "groupname": "Microsoft_Users",
    "active": true
  },
  "accessToken": "jwt_access_token",
  "refreshToken": "jwt_refresh_token"
}
```

## Authentication Flow

1. **User clicks "Login with Microsoft" button**
2. **Frontend redirects to Microsoft OAuth endpoint**
3. **User authenticates with Microsoft**
4. **Microsoft redirects to `/redirect` page with authorization code**
5. **MSAL exchanges code for access token**
6. **Frontend sends access token to backend API**
7. **Backend validates token with Microsoft Graph API**
8. **Backend creates/updates user and generates JWT tokens**
9. **Frontend stores tokens and redirects to application**

## Database Schema

### User Entity Updates

```sql
ALTER TABLE users ADD COLUMN microsoftId VARCHAR(255);
ALTER TABLE users ADD COLUMN displayName VARCHAR(255);
CREATE INDEX "IDX_USER_MICROSOFT_ID" ON "users" ("microsoftId");
```

## Security Considerations

1. **Token Validation:** All Microsoft access tokens are validated against Microsoft Graph API
2. **User Linking:** Existing users can be linked to Microsoft accounts via email matching
3. **Account Creation:** New users are created with secure random passwords
4. **JWT Security:** Standard JWT tokens are used for session management
5. **Environment Variables:** Sensitive configuration is stored in environment variables

## Troubleshooting

### Common Issues

1. **MSAL Initialization Error:**
   - Ensure REACT_APP_MICROSOFT_CLIENT_ID is set correctly
   - Check Azure Portal redirect URI configuration

2. **Token Validation Failed:**
   - Verify MICROSOFT_CLIENT_SECRET is correct
   - Check Microsoft Graph API permissions

3. **User Creation Failed:**
   - Ensure database migration has been run
   - Check database connection and permissions

### Debug Mode

Enable debug logging by setting:
```javascript
localStorage.setItem('microsoft-oauth-debug', 'true')
```

View logs in browser console or export them:
```javascript
window.microsoftOAuthDebugger.exportLogs()
```

## Testing

### Manual Testing Checklist

- [ ] Microsoft login button appears on login page
- [ ] Clicking button redirects to Microsoft OAuth
- [ ] Successful authentication redirects to `/redirect` page
- [ ] User is created/updated in database
- [ ] JWT tokens are generated and stored
- [ ] User is redirected to application
- [ ] Logout functionality works correctly

### Error Scenarios

- [ ] Invalid access token handling
- [ ] Network error handling
- [ ] User cancellation handling
- [ ] Expired token handling

## Dependencies

### Backend
- `@azure/msal-node`: Microsoft Authentication Library for Node.js
- `axios`: HTTP client for Microsoft Graph API calls

### Frontend
- `@azure/msal-browser`: Microsoft Authentication Library for browsers
- `@azure/msal-react`: React wrapper for MSAL

## Support

For issues and troubleshooting:
1. Check the debug logs using the microsoftOAuthDebugger utility
2. Verify Azure Portal configuration
3. Ensure all environment variables are set correctly
4. Review the MICROSOFT_OAUTH_TROUBLESHOOTING.md guide
