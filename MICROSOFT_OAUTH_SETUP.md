# Hướng dẫn cấu hình Microsoft OAuth2 cho Flowise

## 1. Đ<PERSON>ng ký ứng dụng trên Microsoft Azure

### Bước 1: Tạo ứng dụng trên Azure Portal
1. T<PERSON>y cập [Azure Portal](https://portal.azure.com)
2. Vào **Azure Active Directory** > **App registrations**
3. Click **New registration**
4. Điền thông tin:
   - **Name**: Flowise OAuth2
   - **Supported account types**: Accounts in any organizational directory and personal Microsoft accounts
   - **Redirect URI**: 
     - Type: Web
     - URI: `http://localhost:3000/login` (cho development)
     - URI: `https://yourdomain.com/login` (cho production)

### Bước 2: Lấy Client ID
1. <PERSON>u khi tạo xong, copy **Application (client) ID**
2. Đ<PERSON>y sẽ là `REACT_APP_MICROSOFT_CLIENT_ID`

### Bước 3: Cấu hình API permissions
1. Vào **API permissions**
2. Click **Add a permission**
3. Chọn **Microsoft Graph** > **Delegated permissions**
4. Thêm permission: `User.Read`
5. Click **Grant admin consent**

### Bước 4: Tạo Client Secret (nếu cần)
1. Vào **Certificates & secrets**
2. Click **New client secret**
3. Copy giá trị secret (chỉ hiển thị 1 lần)

## 2. Cấu hình Environment Variables

### Frontend (.env)
```env
REACT_APP_MICROSOFT_CLIENT_ID=your-microsoft-client-id
REACT_APP_MICROSOFT_TENANT_ID=your-tenant-id
REACT_APP_MICROSOFT_REDIRECT_URI=http://localhost:3000/login
```

### Backend (.env)
```env
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret
MICROSOFT_TENANT_ID=your-tenant-id
```

## 3. Cài đặt Dependencies

### Backend
```bash
cd packages/server
npm install passport passport-microsoft express-session
npm install --save-dev @types/passport
```

### Frontend
```bash
cd packages/ui
npm install @microsoft/msal-browser @microsoft/msal-react @microsoft/microsoft-graph-client
```

## 4. Cấu hình Database

Chạy migration để thêm các trường mới vào bảng User:
```sql
ALTER TABLE users ADD COLUMN microsoft_id VARCHAR(255);
ALTER TABLE users ADD COLUMN display_name VARCHAR(255);
```

## 5. Luồng hoạt động

1. **User click "Đăng nhập bằng Microsoft"**
2. **Frontend** redirect đến Microsoft OAuth2
3. **User** đăng nhập trên Microsoft
4. **Microsoft** redirect về `/login` với authorization code
5. **Frontend** exchange code lấy access token
6. **Frontend** gửi access token đến Backend
7. **Backend** verify token và lấy user info từ Microsoft Graph API
8. **Backend** tạo hoặc cập nhật user trong database
9. **Backend** trả về JWT token
10. **Frontend** lưu JWT token và redirect về trang chủ

## 6. Troubleshooting

### Lỗi thường gặp:
1. **"AADSTS50011: The reply URL specified in the request does not match the reply URLs configured for the application"**
   - Kiểm tra Redirect URI trong Azure Portal
   - Đảm bảo URI khớp với cấu hình

2. **"AADSTS7000215: Invalid client secret is provided"**
   - Kiểm tra Client Secret
   - Tạo lại Client Secret nếu cần

3. **"User.Read permission not granted"**
   - Kiểm tra API permissions trong Azure Portal
   - Grant admin consent

### Debug:
- Kiểm tra Network tab trong Developer Tools
- Kiểm tra Console logs
- Kiểm tra Backend logs

## 7. Security Considerations

1. **HTTPS**: Sử dụng HTTPS trong production
2. **Client Secret**: Không commit client secret vào code
3. **Redirect URI**: Chỉ allow các domain đáng tin cậy
4. **Token Validation**: Luôn validate token trên backend
5. **Error Handling**: Không expose sensitive information trong error messages 