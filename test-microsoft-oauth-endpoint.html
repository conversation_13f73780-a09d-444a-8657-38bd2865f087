<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Microsoft OAuth Endpoint</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🔐 Test Microsoft OAuth Endpoint</h1>
    
    <div class="test-section">
        <h3>1. Test Backend Endpoint Accessibility</h3>
        <button onclick="testEndpoint()">Test /api/v1/user/login/microsoft</button>
        <div id="endpoint-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. Test with Mock Access Token</h3>
        <input type="text" id="mock-token" placeholder="Nhập mock access token (hoặc để trống để dùng token test)" value="mock_access_token_for_testing">
        <button onclick="testWithMockToken()">Test với Mock Token</button>
        <div id="mock-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. Debug Information</h3>
        <button onclick="showDebugInfo()">Show Debug Info</button>
        <div id="debug-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. Enable Microsoft OAuth Debug</h3>
        <button onclick="enableDebug()">Enable Debug Mode</button>
        <button onclick="showLogs()">Show Debug Logs</button>
        <div id="debug-logs" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = window.location.origin;

        async function testEndpoint() {
            const resultDiv = document.getElementById('endpoint-result');
            resultDiv.textContent = 'Testing endpoint...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/user/login/microsoft`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({})
                });
                
                const data = await response.text();
                
                resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
                resultDiv.textContent = `Status: ${response.status} ${response.statusText}\n\nResponse:\n${data}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function testWithMockToken() {
            const resultDiv = document.getElementById('mock-result');
            const token = document.getElementById('mock-token').value || 'mock_access_token_for_testing';
            
            resultDiv.textContent = 'Testing with mock token...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/user/login/microsoft`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ accessToken: token })
                });
                
                const data = await response.text();
                
                resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
                resultDiv.textContent = `Status: ${response.status} ${response.statusText}\n\nResponse:\n${data}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        function showDebugInfo() {
            const resultDiv = document.getElementById('debug-result');
            
            const info = {
                currentUrl: window.location.href,
                origin: window.location.origin,
                search: window.location.search,
                hash: window.location.hash,
                userAgent: navigator.userAgent,
                localStorage: {
                    dataLogin: localStorage.getItem('dataLogin'),
                    debugMode: localStorage.getItem('microsoft-oauth-debug')
                },
                apiBaseUrl: API_BASE_URL
            };
            
            resultDiv.className = 'result';
            resultDiv.textContent = JSON.stringify(info, null, 2);
        }

        function enableDebug() {
            localStorage.setItem('microsoft-oauth-debug', 'true');
            alert('Debug mode enabled! Refresh the page and try Microsoft login again.');
        }

        function showLogs() {
            const resultDiv = document.getElementById('debug-logs');
            
            if (window.microsoftOAuthDebugger) {
                const logs = window.microsoftOAuthDebugger.getLogs();
                resultDiv.className = 'result';
                resultDiv.textContent = JSON.stringify(logs, null, 2);
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Microsoft OAuth Debugger not found. Make sure you are on the main application page.';
            }
        }
    </script>
</body>
</html>
