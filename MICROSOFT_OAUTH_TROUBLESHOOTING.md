# Microsoft OAuth2 Troubleshooting Guide

## Lỗi thường gặp và cách khắc phục

### 1. Lỗi "uninitialized_public_client_application"

**Lỗi:**
```
uninitialized_public_client_application: You must call and await the initialize function before attempting to call any other MSAL API
```

**Nguyên nhân:**
- MSAL instance chưa được initialize trước khi sử dụng
- Gọi `loginRedirect()` trước khi `initialize()` hoàn thành

**Giải pháp:**
- Sử dụng `useMSAL` hook để quản lý MSAL instance
- Đợi `isInitialized` = true trước khi gọi login
- Sử dụng async/await cho `initialize()`

**Code đã sửa:**
```javascript
// ✅ Đúng cách
const { isInitialized, login } = useMSAL()

const handleLogin = async () => {
  if (!isInitialized) return
  
  try {
    await login(['User.Read'])
  } catch (error) {
    // Handle error
  }
}
```

### 2. Lỗi "AADSTS50011: The reply URL specified in the request does not match"

**Lỗi:**
```
AADSTS50011: The reply URL specified in the request does not match the reply URLs configured for the application
```

**Nguyên nhân:**
- Redirect URI trong Azure Portal không khớp với cấu hình
- Port hoặc domain không đúng

**Giải pháp:**
1. Kiểm tra Azure Portal:
   - Vào App Registration > Authentication
   - Thêm redirect URI: `http://localhost:8080/login`
   - Đảm bảo port khớp với development server

2. Kiểm tra cấu hình Frontend:
```javascript
const msalConfig = {
  auth: {
    redirectUri: window.location.origin + '/login' // Tự động lấy origin
  }
}
```

### 3. Lỗi "AADSTS7000215: Invalid client secret is provided"

**Lỗi:**
```
AADSTS7000215: Invalid client secret is provided
```

**Nguyên nhân:**
- Client secret không đúng hoặc đã hết hạn
- Cấu hình sai trong environment variables

**Giải pháp:**
1. Tạo lại Client Secret trong Azure Portal
2. Cập nhật environment variables
3. Restart application

### 4. Lỗi "User.Read permission not granted"

**Lỗi:**
```
Insufficient privileges to complete the operation
```

**Nguyên nhân:**
- Thiếu API permissions
- Chưa grant admin consent

**Giải pháp:**
1. Vào Azure Portal > App Registration > API permissions
2. Thêm permission: `Microsoft Graph > Delegated > User.Read`
3. Click "Grant admin consent"

### 5. Lỗi "No email found in Microsoft user data"

**Lỗi:**
```
Không thể lấy email từ tài khoản Microsoft
```

**Nguyên nhân:**
- User không có email trong Microsoft account
- Thiếu quyền đọc email

**Giải pháp:**
1. Kiểm tra Microsoft account có email không
2. Thêm permission `User.Read` hoặc `User.ReadBasic.All`
3. Sử dụng `userPrincipalName` thay vì `mail`

### 6. Lỗi Network/CORS

**Lỗi:**
```
CORS error hoặc Network error
```

**Nguyên nhân:**
- CORS configuration không đúng
- Network connectivity issues

**Giải pháp:**
1. Kiểm tra CORS settings trong Backend
2. Đảm bảo HTTPS trong production
3. Kiểm tra firewall/network

## Debug Steps

### 1. Kiểm tra Environment Variables

```bash
# Frontend (.env)
REACT_APP_MICROSOFT_CLIENT_ID=your-client-id

# Backend (.env)
MICROSOFT_CLIENT_ID=your-client-id
MICROSOFT_CLIENT_SECRET=your-client-secret
```

### 2. Kiểm tra Azure Portal Configuration

1. **App Registration:**
   - Client ID đúng
   - Redirect URIs đúng
   - API permissions đã grant

2. **Authentication:**
   - Platform configuration đúng
   - Redirect URIs match

### 3. Sử dụng Debug Tools

```javascript
// Bật debug mode
localStorage.setItem('microsoft-oauth-debug', 'true')

// Xem logs trong console
console.log(window.microsoftOAuthDebugger.getLogs())

// Export logs
window.microsoftOAuthDebugger.exportLogs()
```

### 4. Kiểm tra Network Tab

1. Mở Developer Tools (F12)
2. Vào Network tab
3. Thực hiện login
4. Kiểm tra các requests:
   - Microsoft OAuth requests
   - Backend API calls
   - Error responses

### 5. Kiểm tra Console Logs

```javascript
// Frontend logs
🔐 [Microsoft OAuth Frontend]: ...

// Backend logs
🔐 [Microsoft OAuth]: ...
🔐 [Microsoft OAuth Controller]: ...
```

## Testing Checklist

### Frontend Testing:
- [ ] MSAL initialization successful
- [ ] Login button clickable
- [ ] Redirect to Microsoft works
- [ ] Callback handling works
- [ ] Error handling works
- [ ] Loading states correct

### Backend Testing:
- [ ] API endpoint accessible
- [ ] Microsoft Graph API call works
- [ ] User creation/update works
- [ ] JWT token generation works
- [ ] Error responses correct

### Integration Testing:
- [ ] Full login flow works
- [ ] User data saved correctly
- [ ] Session management works
- [ ] Logout works
- [ ] Error scenarios handled

## Performance Optimization

### 1. Lazy Loading
```javascript
// Lazy load MSAL only when needed
const MicrosoftLoginButton = lazy(() => import('./MicrosoftLoginButton'))
```

### 2. Error Boundaries
```javascript
class MicrosoftOAuthErrorBoundary extends Component {
  // Handle MSAL errors gracefully
}
```

### 3. Retry Logic
```javascript
const retryLogin = async (maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await login()
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}
```

## Security Best Practices

1. **Environment Variables:** Không commit secrets vào code
2. **HTTPS:** Sử dụng HTTPS trong production
3. **Token Validation:** Validate tokens trên backend
4. **Error Handling:** Không expose sensitive info trong errors
5. **Logging:** Mask sensitive data trong logs

## Monitoring

### 1. Log Monitoring
- Monitor error logs
- Track login success/failure rates
- Alert on unusual patterns

### 2. Performance Monitoring
- Track login response times
- Monitor Microsoft Graph API calls
- Alert on slow responses

### 3. Security Monitoring
- Monitor failed login attempts
- Track token usage
- Alert on suspicious activities 