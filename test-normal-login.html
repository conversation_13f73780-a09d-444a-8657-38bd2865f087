<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Normal Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🔐 Test Normal Login (Không phải Microsoft OAuth)</h1>
    
    <div class="test-section">
        <h3>1. Test Normal Login Endpoint</h3>
        <label for="username">Username:</label>
        <input type="text" id="username" placeholder="Nhập username" value="admin">
        
        <label for="password">Password:</label>
        <input type="password" id="password" placeholder="Nhập password" value="admin">
        
        <button onclick="testNormalLogin()">Test Normal Login</button>
        <div id="login-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. Test User Endpoint (GET)</h3>
        <button onclick="testGetUser()">Test GET /api/v1/user</button>
        <div id="user-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. Test Database Connection</h3>
        <button onclick="testDatabaseConnection()">Test Database</button>
        <div id="db-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. Debug Information</h3>
        <button onclick="showDebugInfo()">Show Debug Info</button>
        <div id="debug-result" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = window.location.origin;

        async function testNormalLogin() {
            const resultDiv = document.getElementById('login-result');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Vui lòng nhập username và password';
                return;
            }
            
            resultDiv.textContent = 'Testing normal login...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/user/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        username: username,
                        password: password 
                    })
                });
                
                const data = await response.text();
                
                resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
                resultDiv.textContent = `Status: ${response.status} ${response.statusText}\n\nResponse:\n${data}`;
                
                if (response.ok) {
                    try {
                        const jsonData = JSON.parse(data);
                        if (jsonData.accessToken) {
                            localStorage.setItem('testAccessToken', jsonData.accessToken);
                            console.log('Access token saved for testing');
                        }
                    } catch (e) {
                        console.log('Response is not JSON');
                    }
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function testGetUser() {
            const resultDiv = document.getElementById('user-result');
            resultDiv.textContent = 'Testing GET user endpoint...';
            
            try {
                const headers = {
                    'Content-Type': 'application/json',
                };
                
                // Add authorization header if we have a token
                const token = localStorage.getItem('testAccessToken');
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }
                
                const response = await fetch(`${API_BASE_URL}/api/v1/user`, {
                    method: 'GET',
                    headers: headers
                });
                
                const data = await response.text();
                
                resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
                resultDiv.textContent = `Status: ${response.status} ${response.statusText}\n\nResponse:\n${data}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function testDatabaseConnection() {
            const resultDiv = document.getElementById('db-result');
            resultDiv.textContent = 'Testing database connection...';
            
            try {
                // Test any endpoint that requires database
                const response = await fetch(`${API_BASE_URL}/api/v1/ping`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.text();
                
                resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
                resultDiv.textContent = `Status: ${response.status} ${response.statusText}\n\nResponse:\n${data}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        function showDebugInfo() {
            const resultDiv = document.getElementById('debug-result');
            
            const info = {
                currentUrl: window.location.href,
                origin: window.location.origin,
                userAgent: navigator.userAgent,
                localStorage: {
                    testAccessToken: localStorage.getItem('testAccessToken') ? 'Present' : 'Not found',
                    dataLogin: localStorage.getItem('dataLogin') ? 'Present' : 'Not found',
                    debugMode: localStorage.getItem('microsoft-oauth-debug')
                },
                apiBaseUrl: API_BASE_URL,
                timestamp: new Date().toISOString()
            };
            
            resultDiv.className = 'result';
            resultDiv.textContent = JSON.stringify(info, null, 2);
        }

        // Auto-load debug info on page load
        window.onload = function() {
            showDebugInfo();
        };
    </script>
</body>
</html>
